from typing import Annotated

from fastapi import Depends

from salestech_be.common.lifespan import get_db_engine
from salestech_be.core.reporting.connection.postgres_dwh_connection import (
    AsyncPostgresDwhConnection,
)
from salestech_be.core.reporting.service.query_execution_service import (
    QueryExecutionService,
)
from salestech_be.db.dao.reporting_repository import ReportingRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.perm_predicates import require_read_placeholder_access
from salestech_be.util.validation import not_none
from salestech_be.web.api.reporting.engine.schema import (
    EngineQueryRequest,
    QueryPreviewRequest,
    QueryResponse,
)
from salestech_be.web.api_router_ext import ReeAPIRouter
from salestech_be.web.middleware.types import (
    AnnotatedReevoOrganizationId,
)

router = ReeAPIRouter()
logger = get_logger("reporting.engine")


async def get_reporting_repository(
    db_engine: Annotated[DatabaseEngine, Depends(get_db_engine)],
) -> ReportingRepository:
    return ReportingRepository(engine=db_engine)


async def get_dwh_connection() -> AsyncPostgresDwhConnection:
    """Get a connection to the PostgreSQL DWH database using settings."""
    from salestech_be.settings import settings

    connection = AsyncPostgresDwhConnection(
        host=settings.dwh_host,
        port=settings.dwh_port,
        user=settings.dwh_user,
        password=settings.dwh_pass,
        database=settings.dwh_base,
        ssl=False,
    )
    await connection.connect()
    return connection


async def get_query_execution_service(
    dwh_connection: Annotated[AsyncPostgresDwhConnection, Depends(get_dwh_connection)],
    db_engine: Annotated[DatabaseEngine, Depends(get_db_engine)],
) -> QueryExecutionService:
    return QueryExecutionService(
        db_engine=db_engine,
        dwh_connection=dwh_connection,
    )


@router.post(
    "/_query",
    response_model=QueryResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def query_engine(
    request: EngineQueryRequest,
    organization_id: AnnotatedReevoOrganizationId,
    query_execution_service: Annotated[
        QueryExecutionService, Depends(get_query_execution_service)
    ],
) -> QueryResponse:
    """
    Query a dataset or chart with optional filters.

    This endpoint can be used in the following ways:
    1. dataset_id only: Load dataset and execute query
    2. dataset_id + filters: Load dataset, merge filters, and execute query
    3. chart_id only: Load chart, load dataset, and execute query
    4. chart_id + filters: Load chart, load dataset, merge filters, and execute query

    Either dataset_id or chart_id must be provided, but not both.
    """
    logger.info(f"Query engine request: {request}")
    # Execute query based on request type
    if request.dataset_id is not None:
        # Dataset query
        results = await query_execution_service.execute_dataset(
            dataset_id=not_none(request.dataset_id),
            filters=request.filters,
            organization_id=organization_id,
        )
    else:
        # Chart query
        results = await query_execution_service.execute_chart(
            chart_id=not_none(request.chart_id),
            filters=request.filters,
            organization_id=organization_id,
        )

    # Extract column names from the first result
    columns = list(results[0].keys()) if results else []
    return QueryResponse(
        data=results,
        columns=columns,
    )


@router.post(
    "/_preview",
    response_model=QueryResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def preview_query(
    request: QueryPreviewRequest,
    organization_id: AnnotatedReevoOrganizationId,
    query_execution_service: Annotated[
        QueryExecutionService, Depends(get_query_execution_service)
    ],
) -> QueryResponse:
    """
    Preview a query from a raw query config.

    This endpoint is used when creating a new chart to preview the results
    before saving. It takes a QueryConfig directly and executes it.
    """
    results = await query_execution_service.execute_query(
        query_config=request.query_config,
        organization_id=organization_id,
    )

    # Extract column names from the first result
    columns = list(results[0].keys()) if results else []
    return QueryResponse(
        data=results,
        columns=columns,
    )
