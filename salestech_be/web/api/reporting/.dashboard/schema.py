from uuid import UUID

from pydantic import BaseModel

from salestech_be.core.reporting.type.layout import (
    DashboardLayoutConfig,
    DashboardChartLayoutConfig,
)


class DashboardResponse(BaseModel):
    id: UUID
    name: str
    description: str | None = None
    created_at: str
    updated_at: str | None = None
    layout_config: DashboardLayoutConfig | None = None


class DashboardDetailResponse(DashboardResponse):
    pass


class DashboardListResponse(BaseModel):
    dashboards: list[DashboardResponse]
    total: int
    page: int
    page_size: int


class CreateDashboardRequest(BaseModel):
    name: str
    description: str | None = None
    layout_config: DashboardLayoutConfig | None = None


class UpdateDashboardRequest(BaseModel):
    name: str | None = None
    description: str | None = None
    layout_config: DashboardLayoutConfig | None = None


class DashboardChartAssociationResponse(BaseModel):
    id: UUID
    dashboard_id: UUID
    chart_id: UUID
    layout_config: DashboardChartLayoutConfig | None = None


class CreateDashboardChartAssociationRequest(BaseModel):
    dashboard_id: UUID
    chart_id: UUID
    layout_config: DashboardChartLayoutConfig | None = None


class UpdateDashboardChartAssociationRequest(BaseModel):
    layout_config: DashboardChartLayoutConfig | None = None
