from uuid import UUID

from pydantic import BaseModel, Field

from salestech_be.core.reporting.type.query_config import QueryConfig


class DatasetResponse(BaseModel):
    id: UUID
    name: str
    description: str | None = None
    source: str
    type: str
    owner_user_id: UUID
    created_at: str
    updated_at: str | None = None


class DatasetFieldResponse(BaseModel):
    id: UUID
    dataset_id: UUID
    name: str
    display_name: str | None = None
    data_type: str
    is_derived: bool = False
    derivation_expression: str | None = None


class DatasetDetailResponse(DatasetResponse):
    fields: list[DatasetFieldResponse] = Field(default_factory=list)
    query_config: QueryConfig | None = None
    sql_statement: str | None = None
    table_reference: str | None = None


class DatasetListResponse(BaseModel):
    datasets: list[DatasetResponse]
    total: int
    page: int
    page_size: int


class CreateDatasetRequest(BaseModel):
    name: str
    description: str | None = None
    source: str
    type: str
    table_reference: str | None = None
    query_config: QueryConfig | None = None
    sql_statement: str | None = None


class UpdateDatasetRequest(BaseModel):
    name: str | None = None
    description: str | None = None
    table_reference: str | None = None
    query_config: QueryConfig | None = None
    sql_statement: str | None = None


class DatasetQueryRequest(BaseModel):
    filters: dict | None = None
