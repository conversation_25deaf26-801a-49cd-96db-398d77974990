from typing import Annotated
from uuid import UUID

from fastapi import Depends, HTTPException, Query

from salestech_be.common.lifespan import get_db_engine
from salestech_be.db.dao.reporting_repository import ReportingRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.reporting import (
    ReportingDataset,
    ReportingDatasetSource,
    ReportingDatasetType,
)
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.perm_predicates import require_read_placeholder_access
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.reporting.dataset.schema import (
    CreateDatasetRequest,
    DatasetDetailResponse,
    DatasetFieldResponse,
    DatasetListResponse,
    DatasetResponse,
    UpdateDatasetRequest,
)
from salestech_be.web.api_router_ext import ReeAPIRouter
from salestech_be.web.middleware.types import (
    AnnotatedReevoOrganizationId,
    AnnotatedReevoUserId,
)

router = ReeAPIRouter()
logger = get_logger("reporting.dataset")


async def get_reporting_repository(
    db_engine: Annotated[DatabaseEngine, Depends(get_db_engine)],
) -> ReportingRepository:
    return ReportingRepository(engine=db_engine)


@router.get(
    "",
    response_model=DatasetListResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def list_datasets(
    organization_id: AnnotatedReevoOrganizationId,
    reporting_repository: Annotated[
        ReportingRepository, Depends(get_reporting_repository)
    ],
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
) -> DatasetListResponse:
    """List all datasets for an organization."""
    datasets = await reporting_repository.get_datasets(organization_id)

    # Apply pagination
    start_idx = (page - 1) * page_size
    end_idx = start_idx + page_size
    paginated_datasets = datasets[start_idx:end_idx]

    return DatasetListResponse(
        datasets=[
            DatasetResponse(
                id=dataset.id,
                name=dataset.name,
                description=dataset.description,
                source=dataset.source,
                type=dataset.type,
                owner_user_id=dataset.owner_user_id,
                created_at=dataset.created_at.isoformat(),
                updated_at=dataset.updated_at.isoformat()
                if dataset.updated_at
                else None,
            )
            for dataset in paginated_datasets
        ],
        total=len(datasets),
        page=page,
        page_size=page_size,
    )


@router.get(
    "/{dataset_id}",
    response_model=DatasetDetailResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def get_dataset(
    dataset_id: UUID,
    organization_id: AnnotatedReevoOrganizationId,
    reporting_repository: Annotated[
        ReportingRepository, Depends(get_reporting_repository)
    ],
) -> DatasetDetailResponse:
    """Get a dataset by ID."""
    dataset = await reporting_repository.find_dataset_by_id(dataset_id, organization_id)
    if not dataset:
        raise HTTPException(status_code=404, detail="Dataset not found")

    fields = await reporting_repository.get_dataset_fields(dataset_id)

    return DatasetDetailResponse(
        id=dataset.id,
        name=dataset.name,
        description=dataset.description,
        source=dataset.source,
        type=dataset.type,
        owner_user_id=dataset.owner_user_id,
        created_at=dataset.created_at.isoformat(),
        updated_at=dataset.updated_at.isoformat() if dataset.updated_at else None,
        fields=[
            DatasetFieldResponse(
                id=field.id,
                dataset_id=field.dataset_id,
                name=field.name,
                display_name=field.display_name,
                data_type=field.data_type,
                is_derived=field.is_derived,
                derivation_expression=field.derivation_expression,
            )
            for field in fields
        ],
        query_config=dataset.query_config,
        sql_statement=dataset.sql_statement,
        table_reference=dataset.table_reference,
    )


@router.post(
    "",
    response_model=DatasetDetailResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def create_dataset(
    request: CreateDatasetRequest,
    organization_id: AnnotatedReevoOrganizationId,
    user_id: AnnotatedReevoUserId,
    reporting_repository: Annotated[
        ReportingRepository, Depends(get_reporting_repository)
    ],
) -> DatasetDetailResponse:
    """Create a new dataset."""
    # Validate the dataset type
    try:
        dataset_type = ReportingDatasetType(request.type)
    except ValueError:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid dataset type. Valid types: {[t.value for t in ReportingDatasetType]}",
        )

    # Validate the dataset source
    try:
        dataset_source = ReportingDatasetSource(request.source)
    except ValueError:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid dataset source. Valid sources: {[s.value for s in ReportingDatasetSource]}",
        )

    # Create the dataset
    now = zoned_utc_now()
    dataset = ReportingDataset(
        id=UUID(int=0),  # Will be replaced by the database
        name=request.name,
        description=request.description,
        source=dataset_source,
        type=dataset_type,
        table_reference=request.table_reference,
        query_config=request.query_config,
        sql_statement=request.sql_statement,
        owner_user_id=user_id,
        organization_id=organization_id,
        created_at=now,
        created_by_user_id=user_id,
    )

    created_dataset = await reporting_repository.create_dataset(dataset)

    return DatasetDetailResponse(
        id=created_dataset.id,
        name=created_dataset.name,
        description=created_dataset.description,
        source=created_dataset.source,
        type=created_dataset.type,
        owner_user_id=created_dataset.owner_user_id,
        created_at=created_dataset.created_at.isoformat(),
        updated_at=created_dataset.updated_at.isoformat()
        if created_dataset.updated_at
        else None,
        fields=[],
        query_config=created_dataset.query_config,
        sql_statement=created_dataset.sql_statement,
        table_reference=created_dataset.table_reference,
    )


@router.put(
    "/{dataset_id}",
    response_model=DatasetDetailResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def update_dataset(
    dataset_id: UUID,
    request: UpdateDatasetRequest,
    organization_id: AnnotatedReevoOrganizationId,
    user_id: AnnotatedReevoUserId,
    reporting_repository: Annotated[
        ReportingRepository, Depends(get_reporting_repository)
    ],
) -> DatasetDetailResponse:
    """Update an existing dataset."""
    # Get the existing dataset
    dataset = await reporting_repository.find_dataset_by_id(dataset_id, organization_id)
    if not dataset:
        raise HTTPException(status_code=404, detail="Dataset not found")

    # Update the dataset fields
    if request.name is not None:
        dataset.name = request.name
    if request.description is not None:
        dataset.description = request.description
    if request.query_config is not None:
        dataset.query_config = request.query_config
    if request.sql_statement is not None:
        dataset.sql_statement = request.sql_statement

    # Update the audit fields
    dataset.updated_at = zoned_utc_now()
    dataset.updated_by_user_id = user_id

    # Save the updated dataset
    updated_dataset = await reporting_repository.update_dataset(dataset)

    # Get the dataset fields
    fields = await reporting_repository.get_dataset_fields(dataset_id)

    return DatasetDetailResponse(
        id=updated_dataset.id,
        name=updated_dataset.name,
        description=updated_dataset.description,
        source=updated_dataset.source,
        type=updated_dataset.type,
        owner_user_id=updated_dataset.owner_user_id,
        created_at=updated_dataset.created_at.isoformat(),
        updated_at=updated_dataset.updated_at.isoformat()
        if updated_dataset.updated_at
        else None,
        fields=[
            DatasetFieldResponse(
                id=field.id,
                dataset_id=field.dataset_id,
                name=field.name,
                display_name=field.display_name,
                data_type=field.data_type,
                is_derived=field.is_derived,
                derivation_expression=field.derivation_expression,
            )
            for field in fields
        ],
        query_config=updated_dataset.query_config,
        sql_statement=updated_dataset.sql_statement,
        table_reference=updated_dataset.table_reference,
    )
