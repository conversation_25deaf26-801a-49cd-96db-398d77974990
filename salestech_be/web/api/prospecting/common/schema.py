from uuid import UUID

from pydantic import BaseModel

from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.db.dto.prospecting_dto import (
    EMAIL_ENRICH_CREDITS_PER_ENRICHMENT,
    MOBILE_ENRICH_CREDITS_PER_ENRICHMENT,
)
from salestech_be.db.models.person import ProspectingEnrichStatus


class ContactEnrichmentInfo(BaseModel):
    contact_id: UUID | None
    email_enrichment_status: ProspectingEnrichStatus = (
        ProspectingEnrichStatus.NOT_REQUESTED
    )
    phone_enrichment_status: ProspectingEnrichStatus = (
        ProspectingEnrichStatus.NOT_REQUESTED
    )
    actual_email_credits: int = 0
    actual_phone_credits: int = 0

    @classmethod
    def from_contact(
        cls, contact: ContactV2 | None, is_new_contact: bool = False
    ) -> "ContactEnrichmentInfo":
        if not contact:
            return cls(contact_id=None)

        if is_new_contact and contact.primary_email:
            actual_email_credits = EMAIL_ENRICH_CREDITS_PER_ENRICHMENT
            email_enrichment_status = ProspectingEnrichStatus.ENRICHED
        else:
            actual_email_credits = 0
            email_enrichment_status = ProspectingEnrichStatus.NOT_REQUESTED

        if is_new_contact and contact.primary_phone_number:
            actual_phone_credits = MOBILE_ENRICH_CREDITS_PER_ENRICHMENT
            phone_enrichment_status = ProspectingEnrichStatus.ENRICHED
        else:
            actual_phone_credits = 0
            phone_enrichment_status = ProspectingEnrichStatus.NOT_REQUESTED

        return cls(
            contact_id=contact.id,
            email_enrichment_status=email_enrichment_status,
            phone_enrichment_status=phone_enrichment_status,
            actual_email_credits=actual_email_credits,
            actual_phone_credits=actual_phone_credits,
        )
