from uuid import UUID, uuid4

from fastapi.requests import Request

from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.patch_request import specified
from salestech_be.db.dao.prospecting_run_repository import ProspectingRunRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.person import ProspectingEnrichStatus
from salestech_be.db.models.prospecting_run import (
    ListEnrollmentResult,
    ProspectingCompanyRunRequest,
    ProspectingEnrichStatusInfo,
    ProspectingRun,
    ProspectingRunRequest,
    ProspectingRunResult,
    ProspectingRunResultUpdate,
    ProspectingRunStatus,
    ProspectingRunType,
    ProspectingRunUpdate,
    ResourceCredit,
)
from salestech_be.db.models.search_query import ProspectingSearchQueryType
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none

logger = get_logger(__name__)


class ProspectingRunService:
    def __init__(
        self,
        prospecting_run_repository: ProspectingRunRepository,
    ) -> None:
        self.prospecting_run_repository = prospecting_run_repository

    async def create_people_run(
        self,
        user_id: UUID,
        organization_id: UUID,
        run_request: ProspectingRunRequest,
        run_type: ProspectingRunType,
        estimated_credits: list[ResourceCredit],
    ) -> ProspectingRun:
        return not_none(
            await self.prospecting_run_repository.insert(
                ProspectingRun(
                    id=uuid4(),
                    organization_id=organization_id,
                    user_id=user_id,
                    run_type=run_type,
                    estimated_credits=estimated_credits,
                    run_request=run_request,
                    status=ProspectingRunStatus.PENDING,
                    starts_at=zoned_utc_now(),
                    created_at=zoned_utc_now(),
                    search_query_type=ProspectingSearchQueryType.PEOPLE,
                )
            )
        )

    async def create_company_run(
        self,
        user_id: UUID,
        organization_id: UUID,
        run_request: ProspectingCompanyRunRequest,
        run_type: ProspectingRunType,
        estimated_credits: list[ResourceCredit],
        run_results: dict[UUID, ProspectingEnrichStatusInfo] | None = None,
    ) -> ProspectingRun:
        async with self.prospecting_run_repository.engine.begin():
            prospecting_run = not_none(
                await self.prospecting_run_repository.insert(
                    ProspectingRun(
                        id=uuid4(),
                        organization_id=organization_id,
                        user_id=user_id,
                        run_type=run_type,
                        estimated_credits=estimated_credits,
                        run_request=run_request,
                        status=ProspectingRunStatus.PENDING,
                        starts_at=zoned_utc_now(),
                        created_at=zoned_utc_now(),
                        search_query_type=ProspectingSearchQueryType.COMPANY,
                    )
                )
            )
            # Create result records for each company
            if run_results:
                for company_id in run_results:
                    not_none(
                        await self.prospecting_run_repository.insert(
                            ProspectingRunResult(
                                id=uuid4(),
                                prospecting_run_id=prospecting_run.id,
                                organization_id=organization_id,
                                company_id=company_id,
                                contact_id=None,
                                created_at=zoned_utc_now(),
                            )
                        )
                    )
            return prospecting_run

    async def update_run(
        self,
        prospecting_run_id: UUID,
        organization_id: UUID,
        prospecting_run_update: ProspectingRunUpdate,
    ) -> ProspectingRun:
        updates = ProspectingRunUpdate()
        if specified(prospecting_run_update.estimated_credits):
            updates.estimated_credits = prospecting_run_update.estimated_credits
        if specified(prospecting_run_update.actual_credits):
            updates.actual_credits = prospecting_run_update.actual_credits
        if specified(prospecting_run_update.status):
            updates.status = prospecting_run_update.status
        if specified(prospecting_run_update.error_info):
            updates.error_info = prospecting_run_update.error_info
        if specified(prospecting_run_update.starts_at):
            updates.starts_at = prospecting_run_update.starts_at
        if specified(prospecting_run_update.ends_at):
            updates.ends_at = prospecting_run_update.ends_at
        if specified(prospecting_run_update.quota_usage_ids):
            updates.quota_usage_ids = prospecting_run_update.quota_usage_ids

        return not_none(
            await self.prospecting_run_repository.update_run(
                prospecting_run_id=prospecting_run_id,
                organization_id=organization_id,
                updates=updates,
            )
        )

    # bulk update run results for person_ids
    async def update_run_result(
        self,
        *,
        prospecting_run_id: UUID,
        organization_id: UUID,
        person_id: UUID,
        prospecting_run_result_update: ProspectingRunResultUpdate,
    ) -> ProspectingRunResult:
        updates = ProspectingRunResultUpdate()

        # Copy all provided fields
        if prospecting_run_result_update.contact_id:
            updates.contact_id = prospecting_run_result_update.contact_id

        if prospecting_run_result_update.email_enrichment_status:
            updates.email_enrichment_status = (
                prospecting_run_result_update.email_enrichment_status
            )

        if prospecting_run_result_update.actual_email_credits:
            updates.actual_email_credits = (
                prospecting_run_result_update.actual_email_credits
            )

        if prospecting_run_result_update.phone_enrichment_status:
            updates.phone_enrichment_status = (
                prospecting_run_result_update.phone_enrichment_status
            )

        if prospecting_run_result_update.actual_phone_credits:
            updates.actual_phone_credits = (
                prospecting_run_result_update.actual_phone_credits
            )

        if prospecting_run_result_update.list_enrollment_results:
            updates.list_enrollment_results = (
                prospecting_run_result_update.list_enrollment_results
            )

        if prospecting_run_result_update.sequence_enrollment_status:
            updates.sequence_enrollment_status = (
                prospecting_run_result_update.sequence_enrollment_status
            )

        if prospecting_run_result_update.sequence_enrollment_rejection_reason:
            updates.sequence_enrollment_rejection_reason = (
                prospecting_run_result_update.sequence_enrollment_rejection_reason
            )

        return await self.prospecting_run_repository.update_run_result(
            prospecting_run_id=prospecting_run_id,
            organization_id=organization_id,
            person_id=person_id,
            updates=updates,
        )

    # bulk update run results for company_ids
    async def bulk_update_run_results_for_company_ids(
        self,
        *,
        prospecting_run_id: UUID,
        company_ids: list[UUID],
        organization_id: UUID,
        list_enrollment_results: list[ListEnrollmentResult],
    ) -> list[ProspectingRunResult]:
        updates = ProspectingRunResultUpdate(
            list_enrollment_results=list_enrollment_results
        )
        return await self.prospecting_run_repository.bulk_update_run_result_for_company_ids(
            prospecting_run_id=prospecting_run_id,
            company_ids=company_ids,
            organization_id=organization_id,
            updates=updates,
        )

    # bulk update run results for company_ids with account_id
    async def bulk_set_account_ids_for_run_results(
        self,
        *,
        prospecting_run_id: UUID,
        organization_id: UUID,
        company_account_id_map: dict[UUID, UUID],
    ) -> list[ProspectingRunResult]:
        return (
            await self.prospecting_run_repository.bulk_set_account_ids_for_run_results(
                prospecting_run_id=prospecting_run_id,
                organization_id=organization_id,
                company_account_id_map=company_account_id_map,
            )
        )

    async def get_by_id(
        self,
        prospecting_run_id: UUID,
        organization_id: UUID,
    ) -> ProspectingRun:
        return (
            await self.prospecting_run_repository.find_by_tenanted_primary_key_or_fail(
                table_model=ProspectingRun,
                id=prospecting_run_id,
                organization_id=organization_id,
            )
        )

    async def upsert_run_result(
        self,
        *,
        prospecting_run_id: UUID,
        organization_id: UUID,
        person_id: UUID,
        email_enrichment_status: ProspectingEnrichStatus,
        phone_enrichment_status: ProspectingEnrichStatus,
        actual_email_credits: int,
        actual_phone_credits: int,
    ) -> ProspectingRunResult:
        return not_none(
            await self.prospecting_run_repository.upsert_unique_target_columns(
                ProspectingRunResult(
                    id=uuid4(),
                    prospecting_run_id=prospecting_run_id,
                    organization_id=organization_id,
                    person_id=person_id,
                    contact_id=None,
                    email_enrichment_status=email_enrichment_status,
                    phone_enrichment_status=phone_enrichment_status,
                    actual_email_credits=actual_email_credits,
                    actual_phone_credits=actual_phone_credits,
                    created_at=zoned_utc_now(),
                ),
                on_conflict_target_columns=[
                    "prospecting_run_id",
                    "person_id",
                    "organization_id",
                ],
                exclude_columns_from_update=[
                    "created_at",
                    "organization_id",
                    "prospecting_run_id",
                    "person_id",
                ],
            )
        )

    async def list_prospecting_run_results_by_run_id(
        self,
        *,
        prospecting_run_id: UUID,
        organization_id: UUID,
    ) -> list[ProspectingRunResult]:
        return await self.prospecting_run_repository.list_results_by_run_id(
            prospecting_run_id=prospecting_run_id,
        )


class SingletonProspectingRunService(Singleton, ProspectingRunService):
    pass


def get_prospecting_run_service_from_engine(
    db_engine: DatabaseEngine,
) -> ProspectingRunService:
    return SingletonProspectingRunService(
        prospecting_run_repository=ProspectingRunRepository(engine=db_engine),
    )


def get_prospecting_run_service(
    request: Request,
) -> ProspectingRunService:
    db_engine = get_db_engine(request)
    return get_prospecting_run_service_from_engine(db_engine=db_engine)
