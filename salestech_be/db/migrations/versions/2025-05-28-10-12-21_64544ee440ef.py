"""reporting dataset tables and chart table

Revision ID: 64544ee440ef
Revises: 94f0e00d88ce
Create Date: 2025-05-28 10:12:21.433093+00:00

"""

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "64544ee440ef"
down_revision: str | tuple[str, ...] | None = "94f0e00d88ce"
branch_labels: set[str] | str | None = None
depends_on: str | None = None


def upgrade() -> None:
    # Create Dataset table
    op.execute(
        sqltext="""
            CREATE TABLE reporting_dataset (
                "id" UUID PRIMARY KEY,
                "name" TEXT NOT NULL,
                "description" TEXT,
                "source" TEXT NOT NULL,
                "type" TEXT NOT NULL,
                "table_reference" TEXT,
                "query_config" JSONB,
                "sql_statement" TEXT,

                "organization_id" UUID,
                "created_at" TIMESTAMPTZ NOT NULL,
                "created_by_user_id" UUID,
                "updated_at" TIMESTAMPTZ,
                "updated_by_user_id" UUID,
                "deleted_at" TIMESTAMPTZ,
                "deleted_by_user_id" UUID
            );
        """,
    )

    # Create DatasetField table
    op.execute(
        sqltext="""
            CREATE TABLE reporting_dataset_field (
                "id" UUID PRIMARY KEY,
                "dataset_id" UUID NOT NULL REFERENCES reporting_dataset(id) ON DELETE CASCADE,
                "name" TEXT NOT NULL,
                "display_name" TEXT,
                "data_type" TEXT NOT NULL,

                "organization_id" UUID,
                "created_at" TIMESTAMPTZ NOT NULL,
                "created_by_user_id" UUID,
                "updated_at" TIMESTAMPTZ,
                "updated_by_user_id" UUID,
                "deleted_at" TIMESTAMPTZ,
                "deleted_by_user_id" UUID
            );
        """,
    )

    # Create DatasetRelation table
    op.execute(
        sqltext="""
            CREATE TABLE reporting_dataset_relation (
                "id" UUID PRIMARY KEY,
                "source_dataset_id" UUID NOT NULL REFERENCES reporting_dataset(id) ON DELETE CASCADE,
                "target_dataset_id" UUID NOT NULL REFERENCES reporting_dataset(id) ON DELETE CASCADE,
                "source_field_id" UUID NOT NULL REFERENCES reporting_dataset_field(id) ON DELETE CASCADE,
                "target_field_id" UUID NOT NULL REFERENCES reporting_dataset_field(id) ON DELETE CASCADE,
                "join_type" TEXT NOT NULL,

                "organization_id" UUID,
                "created_at" TIMESTAMPTZ NOT NULL,
                "created_by_user_id" UUID,
                "updated_at" TIMESTAMPTZ,
                "updated_by_user_id" UUID,
                "deleted_at" TIMESTAMPTZ,
                "deleted_by_user_id" UUID
            );
        """,
    )

    # Create Chart table
    op.execute(
        sqltext="""
            CREATE TABLE reporting_chart (
                "id" UUID PRIMARY KEY,
                "name" TEXT NOT NULL,
                "description" TEXT,
                "dataset_id" UUID NOT NULL REFERENCES reporting_dataset(id) ON DELETE CASCADE,
                "layout_config" JSONB,
                "is_published" BOOLEAN NOT NULL,

                "organization_id" UUID,
                "created_at" TIMESTAMPTZ NOT NULL,
                "created_by_user_id" UUID,
                "updated_at" TIMESTAMPTZ,
                "updated_by_user_id" UUID,
                "deleted_at" TIMESTAMPTZ,
                "deleted_by_user_id" UUID
            );
        """,
    )

    # Create Dashboard table
    op.execute(
        sqltext="""
            CREATE TABLE reporting_dashboard (
                "id" UUID PRIMARY KEY,
                "name" TEXT NOT NULL,
                "description" TEXT,
                "layout_config" JSONB,

                "organization_id" UUID,
                "created_at" TIMESTAMPTZ NOT NULL,
                "created_by_user_id" UUID,
                "updated_at" TIMESTAMPTZ,
                "updated_by_user_id" UUID,
                "deleted_at" TIMESTAMPTZ,
                "deleted_by_user_id" UUID
            );
        """,
    )

    # Create DashboardChartAssociation table
    op.execute(
        sqltext="""
            CREATE TABLE reporting_dashboard_chart_association (
                "id" UUID PRIMARY KEY,
                "dashboard_id" UUID NOT NULL REFERENCES reporting_dashboard(id) ON DELETE CASCADE,
                "chart_id" UUID NOT NULL REFERENCES reporting_chart(id) ON DELETE CASCADE,
                "layout_config" JSONB,

                "organization_id" UUID,
                "created_at" TIMESTAMPTZ NOT NULL,
                "created_by_user_id" UUID,
                "updated_at" TIMESTAMPTZ,
                "updated_by_user_id" UUID,
                "deleted_at" TIMESTAMPTZ,
                "deleted_by_user_id" UUID
            );
        """,
    )


def downgrade() -> None:
    op.execute(
        sqltext="""
            DROP TABLE reporting_dashboard_chart_association;
        """
    )
    op.execute(
        sqltext="""
            DROP TABLE reporting_dashboard;
        """
    )
    op.execute(
        sqltext="""
            DROP TABLE reporting_chart;
        """
    )
    op.execute(
        sqltext="""
            DROP TABLE reporting_dataset_relation;
        """
    )
    op.execute(
        sqltext="""
            DROP TABLE reporting_dataset_field;
        """
    )
    op.execute(
        sqltext="""
            DROP TABLE reporting_dataset;
        """
    )
