from enum import StrEnum
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field

from salestech_be.common.query_util.filter_schema import FilterSpec
from salestech_be.common.type.patch_request import UNSET, UnsetAware
from salestech_be.db.models.core.base import (
    Column,
    JsonColumn,
    TableBoundedModel,
    TableModel,
)
from salestech_be.db.models.core.constants import TableName
from salestech_be.db.models.person import ProspectingEnrichStatus
from salestech_be.db.models.quota import QuotaConsumingResource
from salestech_be.db.models.search_query import ProspectingSearchQueryType
from salestech_be.util.enum_util import NameValueStrEnum
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now


class ResourceCredit(BaseModel):
    resource: QuotaConsumingResource
    credit: int


class ProspectingRunStatus(StrEnum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class ProspectingEnrichStatusInfo(BaseModel):
    email_enrich_status: ProspectingEnrichStatus
    phone_enrich_status: ProspectingEnrichStatus


class ProspectingRunType(NameValueStrEnum):
    MANUAL = "MANUAL"
    AUTOMATED = "AUTOMATED"
    ASYNC = "ASYNC"


class ProspectingRunRequest(BaseModel):
    person_ids: list[UUID] | None = None
    filter_spec: FilterSpec | None = None
    exclude_person_ids: list[UUID] | None = None
    total_number: int
    enrich_work_email: bool = False
    enrich_phone_numbers: bool = False
    sequence_id: UUID | None = None
    list_ids: list[UUID] | None = None


class ProspectingCompanyRunRequest(BaseModel):
    company_ids: list[UUID] | None = None
    filter_spec: FilterSpec | None = None
    total_number: int
    list_ids: list[UUID] | None = None
    exclude_company_ids: list[UUID] | None = None


class ProspectingBulkEnrichPersonItem(BaseModel):
    contact_id: UUID
    person_id: UUID | None = None
    first_name: str | None = None
    last_name: str | None = None
    display_name: str | None = None
    email: str | None = None
    linkedin_url: str | None = None
    company_name: str | None = None


class ProspectingBulkEnrichPersonRunRequest(BaseModel):
    enrich_requests: list[ProspectingBulkEnrichPersonItem]


class ProspectingRun(TableModel):
    table_name = TableName.prospecting_run
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    organization_id: Column[UUID]
    user_id: Column[UUID]

    quota_usage_ids: Column[list[UUID] | None] = None

    run_type: Column[ProspectingRunType]
    search_query_type: Column[ProspectingSearchQueryType]

    # Credit and quota tracking
    estimated_credits: JsonColumn[list[ResourceCredit] | None] = None
    actual_credits: JsonColumn[list[ResourceCredit] | None] = None

    # request metadata
    run_request: JsonColumn[
        ProspectingRunRequest
        | ProspectingCompanyRunRequest
        | ProspectingBulkEnrichPersonRunRequest
    ]

    # Status and error information
    status: Column[ProspectingRunStatus]
    error_info: Column[str | None] = None

    # Time fields
    starts_at: Column[ZoneRequiredDateTime]
    ends_at: Column[ZoneRequiredDateTime | None] = None
    created_at: Column[ZoneRequiredDateTime]
    updated_at: Column[ZoneRequiredDateTime | None] = None


class ProspectingEnrollmentStatus(NameValueStrEnum):
    not_requested = "not_requested"
    pending = "pending"
    enrolled = "enrolled"
    rejected = "rejected"


class ListEnrollmentResult(BaseModel):
    list_id: UUID
    status: ProspectingEnrollmentStatus
    rejection_reason: str | None = None


class ProspectingRunResult(TableModel):
    table_name = TableName.prospecting_run_result
    ordered_primary_keys = ("id",)

    # Primary key and relationships
    id: Column[UUID]
    prospecting_run_id: Column[UUID]  # Foreign key to ProspectingRun
    organization_id: Column[UUID]
    person_id: Column[UUID | None] = None
    company_id: Column[UUID | None] = None
    contact_id: Column[UUID | None] = None
    account_id: Column[UUID | None] = None

    # Enrichment status
    email_enrichment_status: Column[ProspectingEnrichStatus] = (
        ProspectingEnrichStatus.NOT_REQUESTED
    )
    phone_enrichment_status: Column[ProspectingEnrichStatus] = (
        ProspectingEnrichStatus.NOT_REQUESTED
    )

    # Credit usage for this specific result
    actual_email_credits: Column[int] = 0
    actual_phone_credits: Column[int] = 0

    sequence_enrollment_status: Column[ProspectingEnrollmentStatus | None] = None
    sequence_enrollment_rejection_reason: Column[str | None] = None

    # List enrollment results - stores each list's enrollment status
    list_enrollment_results: JsonColumn[list[ListEnrollmentResult] | None] = None

    # Timestamps for auditing
    created_at: Column[ZoneRequiredDateTime]
    updated_at: Column[ZoneRequiredDateTime | None] = None


class ProspectingRunUpdate(TableBoundedModel[ProspectingRun]):
    model_config = ConfigDict(frozen=False)

    starts_at: UnsetAware[ZoneRequiredDateTime] = UNSET
    ends_at: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    estimated_credits: UnsetAware[list[ResourceCredit]] = UNSET
    actual_credits: UnsetAware[list[ResourceCredit]] = UNSET
    quota_usage_ids: UnsetAware[list[UUID] | None] = UNSET

    status: UnsetAware[ProspectingRunStatus] = UNSET
    error_info: UnsetAware[str | None] = UNSET

    updated_at: ZoneRequiredDateTime = Field(default_factory=zoned_utc_now)


class ProspectingRunResultUpdate(TableBoundedModel[ProspectingRunResult]):
    model_config = ConfigDict(frozen=False)

    email_enrichment_status: UnsetAware[ProspectingEnrichStatus] = UNSET
    phone_enrichment_status: UnsetAware[ProspectingEnrichStatus] = UNSET
    contact_id: UnsetAware[UUID | None] = UNSET
    account_id: UnsetAware[UUID] = UNSET

    actual_email_credits: UnsetAware[int] = UNSET
    actual_phone_credits: UnsetAware[int] = UNSET

    list_enrollment_results: UnsetAware[list[ListEnrollmentResult]] = UNSET
    sequence_enrollment_status: UnsetAware[ProspectingEnrollmentStatus | None] = UNSET
    sequence_enrollment_rejection_reason: UnsetAware[str | None] = UNSET

    updated_at: ZoneRequiredDateTime = Field(default_factory=zoned_utc_now)
