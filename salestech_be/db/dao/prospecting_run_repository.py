from uuid import UUID

from sqlalchemy import text

from salestech_be.common.type.patch_request import UNSET, UnsetAware, specified
from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.dto.prospecting_dto import PeopleEnrichmentSummaryDTO
from salestech_be.db.models.prospecting_run import (
    ProspectingRun,
    ProspectingRunResult,
    ProspectingRunResultUpdate,
    ProspectingRunUpdate,
)
from salestech_be.util.validation import not_none


class ProspectingRunRepository(GenericRepository):
    async def list_by_organization_id(
        self,
        organization_id: UUID,
        only_include_prospecting_run_ids: UnsetAware[set[UUID]] = UNSET,
    ) -> list[ProspectingRun]:
        if specified(only_include_prospecting_run_ids):
            return await self._find_by_column_values(
                ProspectingRun,
                id=list(only_include_prospecting_run_ids),
                organization_id=organization_id,
            )
        else:
            return await self._find_by_column_values(
                ProspectingRun,
                organization_id=organization_id,
            )

    async def list_results_by_run_id(
        self,
        prospecting_run_id: UUID,
    ) -> list[ProspectingRunResult]:
        return await self._find_by_column_values(
            ProspectingRunResult,
            prospecting_run_id=prospecting_run_id,
        )

    async def bulk_update_run_result(
        self,
        prospecting_run_id: UUID,
        person_ids: list[UUID],
        organization_id: UUID,
        updates: ProspectingRunResultUpdate,
    ) -> list[ProspectingRunResult]:
        run_results: list[ProspectingRunResult] = []
        async with self.engine.begin():
            for person_id in person_ids:
                run_result = not_none(
                    await self._update_unique_by_column_values(
                        table_model=ProspectingRunResult,
                        column_value_to_query={
                            "prospecting_run_id": prospecting_run_id,
                            "person_id": person_id,
                            "organization_id": organization_id,
                        },
                        column_to_update=updates,
                    )
                )
                run_results.append(run_result)
        return run_results

    async def bulk_update_run_result_for_company_ids(
        self,
        prospecting_run_id: UUID,
        company_ids: list[UUID],
        organization_id: UUID,
        updates: ProspectingRunResultUpdate,
    ) -> list[ProspectingRunResult]:
        run_results: list[ProspectingRunResult] = []
        async with self.engine.begin():
            for company_id in company_ids:
                run_result = not_none(
                    await self._update_unique_by_column_values(
                        table_model=ProspectingRunResult,
                        column_value_to_query={
                            "prospecting_run_id": prospecting_run_id,
                            "company_id": company_id,
                            "organization_id": organization_id,
                        },
                        column_to_update=updates,
                    )
                )
                run_results.append(run_result)
        return run_results

    async def update_run(
        self,
        prospecting_run_id: UUID,
        organization_id: UUID,
        updates: ProspectingRunUpdate,
    ) -> ProspectingRun:
        return not_none(
            await self._update_unique_by_column_values(
                table_model=ProspectingRun,
                column_value_to_query={
                    "id": prospecting_run_id,
                    "organization_id": organization_id,
                },
                column_to_update=updates,
            )
        )

    async def update_run_result(
        self,
        prospecting_run_id: UUID,
        organization_id: UUID,
        person_id: UUID,
        updates: ProspectingRunResultUpdate,
    ) -> ProspectingRunResult:
        return not_none(
            await self._update_unique_by_column_values(
                table_model=ProspectingRunResult,
                column_value_to_query={
                    "prospecting_run_id": prospecting_run_id,
                    "person_id": person_id,
                    "organization_id": organization_id,
                },
                column_to_update=updates,
            )
        )

    async def bulk_set_account_ids_for_run_results(
        self,
        prospecting_run_id: UUID,
        organization_id: UUID,
        company_account_id_map: dict[UUID, UUID],
    ) -> list[ProspectingRunResult]:
        run_results: list[ProspectingRunResult] = []
        async with self.engine.begin():
            for company_id, account_id in company_account_id_map.items():
                run_result = not_none(
                    await self._update_unique_by_column_values(
                        table_model=ProspectingRunResult,
                        column_value_to_query={
                            "prospecting_run_id": prospecting_run_id,
                            "company_id": company_id,
                            "organization_id": organization_id,
                        },
                        column_to_update=ProspectingRunResultUpdate(
                            account_id=account_id
                        ),
                    )
                )
                run_results.append(run_result)
        return run_results

    async def get_people_enrichment_summary_for_run(
        self,
        prospecting_run_ids: list[UUID],
        organization_id: UUID,
    ) -> list[PeopleEnrichmentSummaryDTO]:
        stmt = text(
            """
            SELECT
            prospecting_run_id,
            SUM(
                CASE WHEN email_enrichment_status != 'not_requested' THEN 1 ELSE 0 END +
                CASE WHEN phone_enrichment_status != 'not_requested' THEN 1 ELSE 0 END
            ) AS total_enrichment_requests_count,
            SUM(CASE
                WHEN email_enrichment_status = 'enriched'
                THEN 1
                ELSE 0
            END) AS successful_email_enrichments_count,
            SUM(CASE
                WHEN phone_enrichment_status = 'enriched'
                THEN 1
                ELSE 0
            END) AS successful_phone_enrichments_count
        FROM prospecting_run_result
        WHERE prospecting_run_id= ANY(:prospecting_run_ids)
        AND organization_id = :organization_id
        GROUP BY prospecting_run_id
        ORDER BY prospecting_run_id
            """
        ).bindparams(
            prospecting_run_ids=prospecting_run_ids,
            organization_id=organization_id,
        )
        rows = await self.engine.all(stmt)
        return await PeopleEnrichmentSummaryDTO.bulk_from_rows(rows=rows)


class ProspectingRunResultRepository(GenericRepository):
    async def list_by_run_id(
        self,
        prospecting_run_id: UUID,
        organization_id: UUID,
    ) -> list[ProspectingRunResult]:
        return await self._find_by_column_values(
            ProspectingRunResult,
            prospecting_run_id=prospecting_run_id,
            organization_id=organization_id,
        )
