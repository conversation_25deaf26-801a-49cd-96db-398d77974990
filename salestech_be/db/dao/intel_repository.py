import uuid
from datetime import datetime
from typing import Any

from sqlalchemy import text

from salestech_be.core.research_agent.types import (
    DBAccountAISuggestedValues,
    DBContactAISuggestedValues,
    ResearchStatus,
)
from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.models.intel_company import (
    IntelCompany,
    IntelMetadata,
)
from salestech_be.db.models.intel_company_activity import (
    IntelCompanyActivity,
    IntelCompanyActivityType,
)
from salestech_be.db.models.intel_company_association import IntelCompanyAssociation
from salestech_be.db.models.intel_company_info import (
    IntelCompanyInfo,
    IntelCompanyInfoMetadata,
)
from salestech_be.db.models.intel_company_info_history import IntelCompanyInfoHistory
from salestech_be.db.models.intel_person import (
    IntelPerson,
)
from salestech_be.db.models.intel_person_activity import (
    IntelPersonActivity,
    IntelPersonActivityType,
)
from salestech_be.db.models.intel_person_association import IntelPersonAssociation
from salestech_be.db.models.intel_person_info import (
    IntelPersonInfo,
    IntelPersonInfoMetadata,
)
from salestech_be.db.models.intel_person_info_history import IntelPersonInfoHistory
from salestech_be.integrations.s3.s3_manager_ext_research import IntelType
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now

logger = get_logger()


class IntelRepository(GenericRepository):
    async def get_intel_person_association_by_contact_id(
        self, contact_id: uuid.UUID
    ) -> IntelPersonAssociation | None:
        return await self._find_unique_by_column_values(
            IntelPersonAssociation, contact_id=contact_id
        )

    async def get_intel_company_association_by_account_id(
        self, account_id: uuid.UUID
    ) -> IntelCompanyAssociation | None:
        return await self._find_unique_by_column_values(
            IntelCompanyAssociation,
            account_id=account_id,
        )

    async def get_or_create_intel_person_by_linkedin_url(
        self, linkedin_url: str
    ) -> IntelPerson:
        intel_person = await self._find_unique_by_column_values(
            IntelPerson,
            linkedin=linkedin_url,
        )
        if intel_person:
            return intel_person

        return await self.insert(
            IntelPerson(
                id=uuid.uuid4(),
                linkedin=linkedin_url,
                created_at=zoned_utc_now(),
            )
        )

    async def get_intel_person_by_linkedin_url_and_id(
        self, linkedin_url: str, intel_person_id: uuid.UUID
    ) -> IntelPerson | None:
        return await self._find_unique_by_column_values(
            IntelPerson, linkedin=linkedin_url, id=intel_person_id
        )

    async def create_intel_company(
        self,
        domain: str | None,
        linkedin_url: str | None,
        redirected_domain: str | None,
    ) -> IntelCompany:
        return await self.insert(
            IntelCompany(
                id=uuid.uuid4(),
                domain=domain,
                linkedin_url=linkedin_url,
                redirected_domain=redirected_domain,
                created_at=zoned_utc_now(),
            )
        )

    async def create_intel_company_association(
        self,
        account_id: uuid.UUID,
        intel_company_id: uuid.UUID,
    ) -> IntelCompanyAssociation:
        return await self.insert(
            IntelCompanyAssociation(
                id=uuid.uuid4(),
                account_id=account_id,
                intel_company_id=intel_company_id,
                created_at=zoned_utc_now(),
            )
        )

    async def get_intel_company_by_domain_and_linkedin_url(
        self,
        domain: str | None,
        linkedin_url: str | None,
        is_redirected_domain: bool,
    ) -> IntelCompany | None:
        if not domain and not linkedin_url:
            raise ValueError("Either domain or linkedin_url must be provided")

        column_to_query: dict[str, str] = {}
        if domain:
            column_to_query[
                "redirected_domain" if is_redirected_domain else "domain"
            ] = domain

        if linkedin_url:
            column_to_query["linkedin_url"] = linkedin_url

        return await self._find_unique_by_column_values(
            IntelCompany,
            exclude_deleted_or_archived=True,
            exclude_locked_by_integrity_jobs=True,
            **column_to_query,
        )

    async def get_intel_company_by_id(
        self,
        intel_company_id: uuid.UUID,
    ) -> IntelCompany | None:
        return await self._find_unique_by_column_values(
            table_model=IntelCompany, id=intel_company_id
        )

    async def get_intel_person_by_id(
        self,
        intel_person_id: uuid.UUID,
    ) -> IntelPerson | None:
        return await self._find_unique_by_column_values(
            table_model=IntelPerson, id=intel_person_id
        )

    async def exists_intel_person_info_history(
        self,
        intel_person_id: uuid.UUID,
    ) -> bool:
        return (
            await self._count_by_column_values(
                IntelPersonInfoHistory,
                intel_person_id=intel_person_id,
            )
            > 0
        )

    async def exists_intel_company_info_history(
        self,
        intel_company_id: uuid.UUID,
    ) -> bool:
        return (
            await self._count_by_column_values(
                IntelCompanyInfoHistory,
                intel_company_id=intel_company_id,
            )
            > 0
        )

    async def exists_intel_company_activity(
        self,
        intel_company_id: uuid.UUID,
        activity_type: IntelCompanyActivityType,
        source_url: str | None = None,
        published_at: datetime | None = None,
        share_urn: str | None = None,
    ) -> bool:
        if share_urn:  # check by json query activity_data->'share_urn'
            raw_stmt = self._get_find_by_column_values_statement(
                IntelCompanyActivity,
                exclude_deleted_or_archived=True,
                intel_company_id=intel_company_id,
                activity_type=activity_type.value,
            )
            final_stmt = text(f"""{raw_stmt}
                and activity_data->'linkedin_post'->>'share_urn' = :share_urn
            """).bindparams(
                intel_company_id=intel_company_id,
                activity_type=activity_type.value,
                share_urn=share_urn,
            )
            checked_rows = await self.engine.all(final_stmt)
            return bool(checked_rows)
        # NOTE when needed we could also check inside the json like:
        # and :activity_date = (activity_data->'funding_milestone'->>'date')::timestamptz
        column_kvs: dict[str, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if source_url:
            column_kvs["source_url"] = source_url
        if published_at:
            column_kvs["published_at"] = published_at
        rows = await self._find_by_column_values(
            IntelCompanyActivity,
            unique=False,
            exclude_deleted_or_archived=True,
            intel_company_id=intel_company_id,
            activity_type=activity_type.value,
            **column_kvs,
        )
        return bool(rows)

    async def exists_intel_person_activity(
        self,
        intel_person_id: uuid.UUID,
        activity_type: IntelPersonActivityType,
        source_url: str | None = None,
        share_urn: str | None = None,
    ) -> bool:
        if share_urn:  # check by json query activity_data->'share_urn'
            raw_stmt = self._get_find_by_column_values_statement(
                IntelPersonActivity,
                exclude_deleted_or_archived=True,
                intel_person_id=intel_person_id,
                activity_type=activity_type.value,
            )
            final_stmt = text(f"""{raw_stmt}
                and activity_data->'linkedin_post'->>'share_urn' = :share_urn
            """).bindparams(
                intel_person_id=intel_person_id,
                activity_type=activity_type.value,
                share_urn=share_urn,
            )
            checked_rows = await self.engine.all(final_stmt)
            return bool(checked_rows)
        column_kvs: dict[str, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if source_url:
            column_kvs["source_url"] = source_url
        rows = await self._find_by_column_values(
            IntelPersonActivity,
            unique=False,
            exclude_deleted_or_archived=True,
            intel_person_id=intel_person_id,
            activity_type=activity_type.value,
            **column_kvs,
        )
        return bool(rows)

    async def get_latest_activity_by_intel_id_and_type(
        self,
        intel_id: uuid.UUID,
        intel_type: IntelType,
    ) -> IntelCompanyActivity | IntelPersonActivity | None:
        if intel_type == IntelType.COMPANY:
            statement = text(
                """SELECT * FROM intel_company_activity
                WHERE intel_company_id = :intel_id
                AND deleted_at IS NULL
                ORDER BY published_at DESC LIMIT 1"""
            ).bindparams(intel_id=intel_id)
        else:
            statement = text(
                """SELECT * FROM intel_person_activity
                WHERE intel_person_id = :intel_id
                AND deleted_at IS NULL
                ORDER BY published_at DESC LIMIT 1"""
            ).bindparams(intel_id=intel_id)

        row = await self.engine.first_or_none(statement)
        if row:
            return (
                IntelCompanyActivity.from_row(row)
                if intel_type == IntelType.COMPANY
                else IntelPersonActivity.from_row(row)
            )

        return None

    async def delete_intel_person_association_by_id(
        self, intel_person_association_id: uuid.UUID, user_id: uuid.UUID
    ) -> IntelPersonAssociation | None:
        return await self.update_by_primary_key(
            IntelPersonAssociation,
            primary_key_to_value={"id": intel_person_association_id},
            column_to_update={
                "deleted_at": zoned_utc_now(),
                "deleted_by_user_id": user_id,
            },
        )

    async def delete_intel_person_association_by_contact_id(
        self, contact_id: uuid.UUID, user_id: uuid.UUID | None = None
    ) -> list[IntelPersonAssociation] | None:
        return await self._update_by_column_values(
            IntelPersonAssociation,
            column_value_to_query={"contact_id": contact_id},
            column_to_update={
                "deleted_at": zoned_utc_now(),
                "deleted_by_user_id": user_id,
            },
        )

    async def delete_intel_company_association_by_id(
        self, intel_company_association_id: uuid.UUID, user_id: uuid.UUID
    ) -> IntelCompanyAssociation | None:
        return await self.update_by_primary_key(
            IntelCompanyAssociation,
            primary_key_to_value={"id": intel_company_association_id},
            column_to_update={
                "deleted_at": zoned_utc_now(),
                "deleted_by_user_id": user_id,
            },
        )

    async def delete_intel_company_association_by_account_id(
        self, account_id: uuid.UUID, user_id: uuid.UUID | None = None
    ) -> list[IntelCompanyAssociation] | None:
        return await self._update_by_column_values(
            IntelCompanyAssociation,
            column_value_to_query={"account_id": account_id},
            column_to_update={
                "deleted_at": zoned_utc_now(),
                "deleted_by_user_id": user_id,
            },
        )

    async def get_intel_company_info_by_intel_id(
        self, intel_id: uuid.UUID
    ) -> IntelCompanyInfo | None:
        return await self._find_unique_by_column_values(
            IntelCompanyInfo,
            intel_company_id=intel_id,
        )

    async def get_intel_person_info_by_intel_id(
        self, intel_id: uuid.UUID
    ) -> IntelPersonInfo | None:
        return await self._find_unique_by_column_values(
            IntelPersonInfo,
            intel_person_id=intel_id,
        )

    async def update_intel_person_status(
        self,
        intel_person_id: uuid.UUID,
        status: ResearchStatus,
    ) -> None:
        await self.update_by_primary_key(
            IntelPerson,
            primary_key_to_value={"id": intel_person_id},
            column_to_update={"status": status.value},
        )

    async def update_intel_company_status(
        self,
        intel_company_id: uuid.UUID,
        status: ResearchStatus,
    ) -> None:
        await self.update_by_primary_key(
            IntelCompany,
            primary_key_to_value={"id": intel_company_id},
            column_to_update={"status": status.value},
        )

    async def update_intel_person_info_metadata(
        self,
        intel_person_id: uuid.UUID,
        metadata: IntelPersonInfoMetadata,
    ) -> None:
        await self._update_by_column_values(
            IntelPersonInfo,
            column_value_to_query={"intel_person_id": intel_person_id},
            column_to_update={"metadata": metadata},
        )

    async def update_intel_company_info_metadata(
        self,
        intel_company_id: uuid.UUID,
        metadata: IntelCompanyInfoMetadata,
    ) -> None:
        await self._update_by_column_values(
            IntelCompanyInfo,
            column_value_to_query={"intel_company_id": intel_company_id},
            column_to_update={"metadata": metadata},
        )

    async def upsert_intel_person_metadata(
        self,
        intel_person_id: uuid.UUID,
        metadata: IntelMetadata,
    ) -> None:
        await self.upsert_unique_target_columns(
            IntelPerson(
                id=intel_person_id,
                metadata=metadata,
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
            ),
            columns_to_update=["metadata", "updated_at"],
            on_conflict_target_columns=["id"],
            exclude_deleted_or_archived=True,
        )

    async def upsert_intel_company_metadata(
        self,
        intel_company_id: uuid.UUID,
        metadata: IntelMetadata,
    ) -> None:
        await self.upsert_unique_target_columns(
            IntelCompany(
                id=intel_company_id,
                metadata=metadata,
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
            ),
            columns_to_update=["metadata", "updated_at"],
            on_conflict_target_columns=["id"],
            exclude_deleted_or_archived=True,
        )

    async def update_intel_company_metadata_from_obj(
        self,
        intel_company_id: uuid.UUID,
        metadata_obj: IntelMetadata,
    ) -> None:
        """
        Update IntelCompanyMetadata fields based on the provided metadata object.
        Only updates fields that are not None in the provided metadata object.

        Args:
            intel_company_id: The ID of the IntelCompany to update
            metadata_obj: The metadata object containing fields to update
        """
        intel_company = await self.get_intel_company_by_id(intel_company_id)
        if not intel_company:
            return

        existing_metadata = intel_company.metadata or IntelMetadata()

        # Update fields that are not None in the provided metadata object
        for field_name, field_value in metadata_obj.model_dump(
            exclude_none=True
        ).items():
            setattr(existing_metadata, field_name, field_value)

        await self.upsert_intel_company_metadata(
            intel_company_id,
            existing_metadata,
        )

    async def update_intel_person_metadata_from_obj(
        self,
        intel_person_id: uuid.UUID,
        metadata_obj: IntelMetadata,
    ) -> None:
        """
        Update IntelPersonMetadata fields based on the provided metadata object.
        Only updates fields that are not None in the provided metadata object.

        Args:
            intel_person_id: The ID of the IntelPerson to update
            metadata_obj: The metadata object containing fields to update
        """
        async with self.engine.begin():
            intel_person = await self.get_intel_person_by_id(intel_person_id)
            if not intel_person:
                return

            existing_metadata = intel_person.metadata or IntelMetadata()

            # Update fields that are not None in the provided metadata object
            for field_name, field_value in metadata_obj.model_dump(
                exclude_none=True
            ).items():
                setattr(existing_metadata, field_name, field_value)

            await self.upsert_intel_person_metadata(
                intel_person_id,
                existing_metadata,
            )

    async def map_ai_suggested_values_by_account_ids(
        self, account_ids: set[uuid.UUID], max_chunk_size: int = 500
    ) -> dict[uuid.UUID, DBAccountAISuggestedValues]:
        if not account_ids:
            return {}

        # Handle large input sets by chunking account_ids
        account_id_chunks: list[list[uuid.UUID]] = [
            list(account_ids)[i : i + max_chunk_size]
            for i in range(0, len(account_ids), max_chunk_size)
        ]

        account_id_to_suggested_values: dict[uuid.UUID, DBAccountAISuggestedValues] = {}

        # Process each chunk separately
        for account_id_chunk in account_id_chunks:
            if not account_id_chunk:
                continue

            # Use proper SQLAlchemy parameter binding for the IN clause
            stmt = text("""
            SELECT
                ica.account_id as account_id,
                ici.data -> 'company_info' -> 'estimated_revenue_higher_bound_usd' as revenue,
                ici.data -> 'company_info' -> 'employee_count_range' as employee_count_range,
                ici.data -> 'company_info' -> 'linkedin_company_description' as linkedin_company_description,
                ici.data -> 'company_info' -> 'taxonomy' -> 'linkedin_industries' as linkedin_industries,
                ici.data -> 'company_info' -> 'company_website' as company_website,
                ici.updated_at as updated_at
            FROM
                intel_company_association ica
            JOIN
                intel_company_info ici ON ica.intel_company_id = ici.intel_company_id
            WHERE
                ica.account_id = ANY(:account_ids)
                AND ici.data is not null
                AND ica.deleted_at IS NULL;
            """).bindparams(account_ids=account_id_chunk)

            results = await self.engine.all(stmt)

            for row in results:
                account_id = row[0]
                account_id_to_suggested_values[account_id] = DBAccountAISuggestedValues(
                    estimated_revenue_higher_bound_usd=row[1],
                    employee_count_range=row[2],
                    linkedin_company_description=row[3],
                    linkedin_industries=row[4],
                    company_website=row[5],
                    updated_at=row[6],
                )

        return account_id_to_suggested_values

    async def map_ai_suggested_values_by_contact_ids(
        self, contact_ids: set[uuid.UUID], max_chunk_size: int = 500
    ) -> dict[
        uuid.UUID, DBContactAISuggestedValues
    ]:  # TODO: Replace Any with proper type
        if not contact_ids:
            return {}

        contact_id_chunks: list[list[uuid.UUID]] = [
            list(contact_ids)[i : i + max_chunk_size]
            for i in range(0, len(contact_ids), max_chunk_size)
        ]

        ai_suggested_values_by_contact_ids: dict[
            uuid.UUID, DBContactAISuggestedValues
        ] = {}

        # Process in chunks to avoid potential DB issues with large IN clauses
        for chunk in contact_id_chunks:
            if not chunk:
                continue
            stmt = text("""
            SELECT
                ipa.contact_id as contact_id,
                ipi.data -> 'linkedin_profile' -> 'linkedin_flagship_url' as linkedin_profile_url,
                ipi.data -> 'linkedin_profile' -> 'title' as title,
                ipi.data -> 'linkedin_profile' -> 'name' as name,
                ipi.updated_at as updated_at
            FROM
                intel_person_association ipa
            JOIN
                intel_person_info ipi ON ipa.intel_person_id = ipi.intel_person_id
            WHERE
                ipa.contact_id = ANY(:contact_ids)
                AND ipi.data is not null
                AND ipa.deleted_at IS NULL;
            """).bindparams(contact_ids=chunk)

            results = await self.engine.all(stmt)

            for row in results:
                contact_id = row._mapping["contact_id"]
                ai_suggested_values_by_contact_ids[contact_id] = (
                    DBContactAISuggestedValues(
                        linkedin_profile_url=row._mapping["linkedin_profile_url"],
                        title=row._mapping["title"],
                        name=row._mapping["name"],
                        updated_at=row._mapping["updated_at"],
                    )
                )

        return ai_suggested_values_by_contact_ids
