import asyncio
import os
import uuid

from salestech_be.db.dao.reporting_repository import ReportingRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.reporting import (
    ReportingDataset,
    ReportingDatasetSource,
    ReportingDatasetType,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.time import zoned_utc_now

logger = get_logger(__name__)


async def get_db_engine() -> DatabaseEngine:
    engine = DatabaseEngine(
        str(settings.db_url),
        echo=settings.db_echo,
        pool_size=settings.db_pool_size,
        max_overflow=settings.db_max_overflow,
    )
    if settings.db_conn_prewarm:
        await engine.prewarm_db_connection()
    return engine


def read_dataset_files() -> dict[str, str]:
    """Read all SQL files from the dataset directory and return their content."""
    dataset_dir = os.path.join(os.path.dirname(__file__), "..", "dataset")
    dataset_files = {}

    try:
        # Get all .sql files from the datasets directory
        for filename in os.listdir(dataset_dir):
            if filename.endswith(".sql"):
                file_path = os.path.join(dataset_dir, filename)
                with open(file_path) as f:
                    # Store filename without .sql extension as key and content as value
                    dataset_files[filename[:-4]] = f.read()

        logger.info(f"Found {len(dataset_files)} dataset files")
        return dataset_files
    except FileNotFoundError:
        logger.error(f"Dataset directory not found at {dataset_dir}")
        raise
    except Exception as e:
        logger.error(f"Error reading dataset files: {e!s}")
        raise


async def insert_template_datasets() -> None:
    """Insert all template datasets into the database."""
    db_engine = await get_db_engine()
    reporting_repository = ReportingRepository(engine=db_engine)

    # Read all dataset files
    dataset_files = read_dataset_files()

    now = zoned_utc_now()

    for dataset_name, sql_content in dataset_files.items():
        try:
            # Create the dataset model
            dataset = ReportingDataset(
                id=uuid.uuid4(),
                name=dataset_name,
                description=None,
                source=ReportingDatasetSource.TEMPLATE,
                type=ReportingDatasetType.SQL,
                table_reference=None,
                query_config=None,
                sql_statement=sql_content,
                organization_id=None,  # Template datasets are not org-specific
                created_at=now,
                created_by_user_id=None,
                updated_at=None,
                updated_by_user_id=None,
                deleted_at=None,
                deleted_by_user_id=None,
            )

            # Insert the dataset
            inserted_dataset = await reporting_repository.create_dataset(dataset)
            logger.info(f"Inserted dataset: {dataset_name} with ID: {inserted_dataset.id}")

        except Exception as e:
            logger.error(f"Error inserting dataset {dataset_name}: {e!s}")
            raise

    logger.info(f"Successfully inserted {len(dataset_files)} template datasets")


async def main() -> None:
    """Main function to run the backfill script."""
    logger.info("Starting template dataset insertion")
    await insert_template_datasets()
    logger.info("Template dataset insertion completed")


if __name__ == "__main__":
    asyncio.run(main())
