WITH

-- Get select list value with outcome_state
pipeline_stage_outcome AS (
  SELECT slv.*,
  psslv.outcome_state
  FROM select_list_value slv
  JOIN pipeline_stage_select_list_value_metadata psslv on psslv.select_list_value_id = slv.id
  WHERE TRUE
  {{ and_filter_values('organization_id', col_alias='slv.organization_id') }}
  {{ and_filter_values('select_list_id', col_alias='slv.select_list_id') }}
),

-- Get pipeline map stage
pipeline_map_stage AS (
  SELECT
    s1.id from_id,
    s1.display_value from_name,
    s1.rank from_rank,
    COALESCE(s2.id, s1.id) to_id,
    COALESCE(s2.display_value, s1.display_value) to_name,
    COALESCE(s2.rank, s1.rank) to_rank
  FROM pipeline_stage_outcome s1
  LEFT JOIN pipeline_stage_outcome s2 ON s1.mapped_output_value_id = s2.id
),

-- Get pipeline duration
pipeline_duration AS (
  SELECT
    p.id,
    EXTRACT(EPOCH FROM (NOW() - t.created_at)) / 86400 AS duration_in_current_stage_days,
    pms.to_id effective_stage_id,
    pms.to_name effective_stage_display_value
  FROM pipeline p
  JOIN pipeline_tracking t
    ON t.pipeline_id = p.id
    AND t.field_name = 'stage_id'
    AND t.is_current IS TRUE
    AND t.organization_id = p.organization_id
  JOIN pipeline_map_stage pms
    ON pms.from_id = p.stage_id
  WHERE p.archived_at IS NULL
  {{ and_filter_values('organization_id', col_alias='p.organization_id') }}
  {{ and_filter_values('owner_user_id', col_alias='p.owner_user_id') }}
),

-- Get median duration for each pipeline stage
pipeline_stage_duration AS (
  SELECT
    effective_stage_id,
    COUNT(*) pipeline_count,
    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY duration_in_current_stage_days) AS median_duration_days
  FROM pipeline_duration
  GROUP BY effective_stage_id
),

-- Get pipeline stage stats of count and median duration
-- with zero count and median duration if no pipeline in the stage
pipeline_all_stage_count_age AS (
  SELECT
    ps.id pipeline_stage_id,
    ps.display_value pipeline_stage_name,
    ps.rank pipeline_stage_rank,
    COALESCE(psd.pipeline_count, 0) pipeline_count,
    ROUND(COALESCE(psd.median_duration_days, 0)::numeric, 2) median_duration_days
  FROM pipeline_stage_outcome ps
  LEFT JOIN pipeline_stage_duration psd ON psd.effective_stage_id = ps.id
  WHERE ps.deleted_at IS NULL AND ps.outcome_state IS NULL
  ORDER BY ps.rank
)

SELECT * FROM pipeline_all_stage_count_age
