-- Example
-- Suppose Now is 2025-04-25 !!!!
-- Suppose Now is 2025-04-25 !!!!
-- Suppose Now is 2025-04-25 !!!!

-- pipeline_map_tracking
--  id | organization_id | pipeline_id | from_stage | to_stage |     created_at
-- ----+-----------------+-------------+------------+----------+---------------------
--   1 | A               | A1          |            |        1 | 2025-01-02 00:00:00
--   2 | A               | A1          |          1 |        2 | 2025-02-03 00:00:00
--   3 | A               | A1          |          2 |        3 | 2025-02-04 00:00:00
--   4 | A               | A1          |          3 |        4 | 2025-04-05 00:00:00
--   5 | B               | B1          |          1 |        2 | 2025-01-10 00:00:00
--   6 | B               | B1          |          2 |        3 | 2025-04-10 00:00:00
--   7 | B               | B2          |          1 |        2 | 2025-02-20 00:00:00
--   8 | B               | B2          |          2 |        3 | 2025-03-20 00:00:00
--   9 | C               | C1          |            |        1 | 2025-01-01 00:00:00

-- pipeline_tracking_dates
--  organization_id | pipeline_id |   min_created_at    |   max_created_at
-- -----------------+-------------+---------------------+---------------------
--  A               | A1          | 2025-01-02 00:00:00 | 2025-04-05 00:00:00
--  B               | B1          | 2025-01-10 00:00:00 | 2025-04-10 00:00:00
--  B               | B2          | 2025-02-20 00:00:00 | 2025-03-20 00:00:00
--  C               | C1          | 2025-01-01 00:00:00 | 2025-01-01 00:00:00

-- pipeline_tracking_bucket_dates
--  organization_id | pipeline_id |        boundary
-- -----------------+-------------+------------------------
--  A               | A1          | 2025-01-01 00:00:00+00
--  A               | A1          | 2025-02-01 00:00:00+00
--  A               | A1          | 2025-03-01 00:00:00+00
--  A               | A1          | 2025-04-01 00:00:00+00
--  B               | B1          | 2025-01-01 00:00:00+00
--  B               | B1          | 2025-02-01 00:00:00+00
--  B               | B1          | 2025-03-01 00:00:00+00
--  B               | B1          | 2025-04-01 00:00:00+00
--  B               | B2          | 2025-02-01 00:00:00+00
--  B               | B2          | 2025-03-01 00:00:00+00
--  B               | B2          | 2025-04-01 00:00:00+00
--  C               | C1          | 2025-01-01 00:00:00+00
--  C               | C1          | 2025-02-01 00:00:00+00
--  C               | C1          | 2025-03-01 00:00:00+00
--  C               | C1          | 2025-04-01 00:00:00+00

-- pipeline_tracking_bucket_boundary
--  organization_id | pipeline_id |     lower_boundary     |     upper_boundary
-- -----------------+-------------+------------------------+------------------------
--  A               | A1          | 2025-01-01 00:00:00+00 | 2025-02-01 00:00:00+00
--  A               | A1          | 2025-02-01 00:00:00+00 | 2025-03-01 00:00:00+00
--  A               | A1          | 2025-03-01 00:00:00+00 | 2025-04-01 00:00:00+00
--  A               | A1          | 2025-04-01 00:00:00+00 | 2025-05-01 00:00:00+00
--  B               | B1          | 2025-01-01 00:00:00+00 | 2025-02-01 00:00:00+00
--  B               | B1          | 2025-02-01 00:00:00+00 | 2025-03-01 00:00:00+00
--  B               | B1          | 2025-03-01 00:00:00+00 | 2025-04-01 00:00:00+00
--  B               | B1          | 2025-04-01 00:00:00+00 | 2025-05-01 00:00:00+00
--  B               | B2          | 2025-02-01 00:00:00+00 | 2025-03-01 00:00:00+00
--  B               | B2          | 2025-03-01 00:00:00+00 | 2025-04-01 00:00:00+00
--  B               | B2          | 2025-04-01 00:00:00+00 | 2025-05-01 00:00:00+00
--  C               | C1          | 2025-01-01 00:00:00+00 | 2025-02-01 00:00:00+00
--  C               | C1          | 2025-02-01 00:00:00+00 | 2025-03-01 00:00:00+00
--  C               | C1          | 2025-03-01 00:00:00+00 | 2025-04-01 00:00:00+00
--  C               | C1          | 2025-04-01 00:00:00+00 | 2025-05-01 00:00:00+00

-- pipeline_bucket_stage
--  organization_id | pipeline_id |     lower_boundary     |     upper_boundary     | tracking_id | stage | tracking_created_at
-- -----------------+-------------+------------------------+------------------------+-------------+-------+---------------------
--  A               | A1          | 2025-01-01 00:00:00+00 | 2025-02-01 00:00:00+00 |           1 |     1 | 2025-01-02 00:00:00
--  A               | A1          | 2025-02-01 00:00:00+00 | 2025-03-01 00:00:00+00 |           3 |     3 | 2025-02-04 00:00:00
--  A               | A1          | 2025-03-01 00:00:00+00 | 2025-04-01 00:00:00+00 |           3 |     3 | 2025-02-04 00:00:00
--  A               | A1          | 2025-04-01 00:00:00+00 | 2025-05-01 00:00:00+00 |           4 |     4 | 2025-04-05 00:00:00
--  B               | B1          | 2025-01-01 00:00:00+00 | 2025-02-01 00:00:00+00 |           5 |     2 | 2025-01-10 00:00:00
--  B               | B1          | 2025-02-01 00:00:00+00 | 2025-03-01 00:00:00+00 |           5 |     2 | 2025-01-10 00:00:00
--  B               | B1          | 2025-03-01 00:00:00+00 | 2025-04-01 00:00:00+00 |           5 |     2 | 2025-01-10 00:00:00
--  B               | B1          | 2025-04-01 00:00:00+00 | 2025-05-01 00:00:00+00 |           6 |     3 | 2025-04-10 00:00:00
--  B               | B2          | 2025-02-01 00:00:00+00 | 2025-03-01 00:00:00+00 |           7 |     2 | 2025-02-20 00:00:00
--  B               | B2          | 2025-03-01 00:00:00+00 | 2025-04-01 00:00:00+00 |           8 |     3 | 2025-03-20 00:00:00
--  B               | B2          | 2025-04-01 00:00:00+00 | 2025-05-01 00:00:00+00 |           8 |     3 | 2025-03-20 00:00:00
--  C               | C1          | 2025-01-01 00:00:00+00 | 2025-02-01 00:00:00+00 |           9 |     1 | 2025-01-01 00:00:00
--  C               | C1          | 2025-02-01 00:00:00+00 | 2025-03-01 00:00:00+00 |           9 |     1 | 2025-01-01 00:00:00
--  C               | C1          | 2025-03-01 00:00:00+00 | 2025-04-01 00:00:00+00 |           9 |     1 | 2025-01-01 00:00:00
--  C               | C1          | 2025-04-01 00:00:00+00 | 2025-05-01 00:00:00+00 |           9 |     1 | 2025-01-01 00:00:00

-- pipeline_bucket_stage_count
--  organization_id |     lower_boundary     | stage | pipeline_count
-- -----------------+------------------------+-------+----------------
--  A               | 2025-01-01 00:00:00+00 |     1 |              1
--  A               | 2025-02-01 00:00:00+00 |     3 |              1
--  A               | 2025-03-01 00:00:00+00 |     3 |              1
--  A               | 2025-04-01 00:00:00+00 |     4 |              1
--  B               | 2025-01-01 00:00:00+00 |     2 |              1
--  B               | 2025-02-01 00:00:00+00 |     2 |              2
--  B               | 2025-03-01 00:00:00+00 |     2 |              1
--  B               | 2025-03-01 00:00:00+00 |     3 |              1
--  B               | 2025-04-01 00:00:00+00 |     3 |              2
--  C               | 2025-01-01 00:00:00+00 |     1 |              1
--  C               | 2025-02-01 00:00:00+00 |     1 |              1
--  C               | 2025-03-01 00:00:00+00 |     1 |              1
--  C               | 2025-04-01 00:00:00+00 |     1 |              1

WITH

-- Get select list value with outcome_state for later use
select_list_value_outcome AS (
  SELECT
    slv.*,
    psslv.outcome_state
  FROM select_list_value slv
  JOIN pipeline_stage_select_list_value_metadata psslv
    ON psslv.select_list_value_id = slv.id
  WHERE TRUE
  {{ and_filter_values('organization_id', col_alias='slv.organization_id') }}
  {{ and_filter_values('select_list_id', col_alias='slv.select_list_id') }}
),

-- Get pipeline map stage
pipeline_map_stage AS (
  SELECT
    s1.id from_id,
    s1.display_value from_name,
    s1.rank from_rank,
    COALESCE(s2.id, s1.id) to_id,
    COALESCE(s2.display_value, s1.display_value) to_name,
    COALESCE(s2.rank, s1.rank) to_rank
  FROM select_list_value_outcome s1
  LEFT JOIN select_list_value_outcome s2 ON s1.mapped_output_value_id = s2.id
),

-- Get pipeline map tracking
pipeline_map_tracking AS (
  SELECT
    pt.id,
    pt.pipeline_id,
    pt.from_value_uuid,
    pt.to_value_uuid,
    pt.created_at,
    pms.to_id AS to_stage_id,
    pms.to_name AS to_stage_name,
    pms.to_rank AS to_stage_rank
  FROM pipeline_tracking pt
  JOIN pipeline p
    ON p.id = pt.pipeline_id
    AND p.organization_id = pt.organization_id
  LEFT JOIN pipeline_map_stage pms ON pms.from_id = pt.to_value_uuid
  WHERE pt.to_value_uuid IS NOT NULL
    AND pt.created_at IS NOT NULL
    AND pt.field_name = 'stage_id'
    {{ and_filter_values('organization_id', col_alias='pt.organization_id') }}
    {{ and_filter_values('owner_user_id', col_alias='p.owner_user_id') }}
),

-- Cal the min_created_at, max_created_at for each pipeline
pipeline_tracking_dates AS (
  SELECT
    pipeline_id,
    MIN(created_at) min_created_at,
    MAX(created_at) max_created_at
  FROM pipeline_map_tracking
  GROUP BY pipeline_id
),

-- Generate cut points by bucket_size between min_created_at and max_created_at
pipeline_tracking_bucket_dates AS (
  SELECT
    pipeline_id,
    {% if first_filter('date_bucket') and first_filter('date_bucket').val.lower() == 'week' %}
      {% if filter_lower_boundary('created_at') and filter_upper_boundary('created_at') %}
        generate_series(
          DATE_TRUNC('week', '{{ filter_lower_boundary('created_at') }}'::timestamp),
          DATE_TRUNC('week', '{{ filter_upper_boundary('created_at') }}'::timestamp),
          '1 week'::interval
        ) boundary
      {% elif filter_lower_boundary('created_at') %}
        generate_series(
          DATE_TRUNC('week', '{{ filter_lower_boundary('created_at') }}'::timestamp),
          DATE_TRUNC('week', NOW()),
          '1 week'::interval
        ) boundary
      {% elif filter_upper_boundary('created_at') %}
        generate_series(
          DATE_TRUNC('week', min_created_at),
          DATE_TRUNC('week', '{{ filter_upper_boundary('created_at') }}'::timestamp),
          '1 week'::interval
        ) boundary
      {% else %}
        generate_series(
          DATE_TRUNC('week', min_created_at),
          DATE_TRUNC('week', NOW()),
          '1 week'::interval
        ) boundary
      {% endif %}

    {% elif first_filter('date_bucket') and first_filter('date_bucket').val.lower() == 'quarter' %}
      {% if from_dttm and to_dttm %}
        generate_series(
          DATE_TRUNC('quarter', '{{ filter_lower_boundary('created_at') }}'::timestamp),
          DATE_TRUNC('quarter', '{{ filter_upper_boundary('created_at') }}'::timestamp),
          '3 months'::interval
        ) boundary
      {% elif filter_lower_boundary('created_at') %}
        generate_series(
          DATE_TRUNC('quarter', '{{ filter_lower_boundary('created_at') }}'::timestamp),
          DATE_TRUNC('quarter', NOW()),
          '3 months'::interval
        ) boundary
      {% elif filter_upper_boundary('created_at') %}
        generate_series(
          DATE_TRUNC('quarter', min_created_at),
          DATE_TRUNC('quarter', '{{ filter_upper_boundary('created_at') }}'::timestamp),
          '3 months'::interval
        ) boundary
      {% else %}
        generate_series(
          DATE_TRUNC('quarter', min_created_at),
          DATE_TRUNC('quarter', NOW()),
          '3 months'::interval
        ) boundary
      {% endif %}

    {% elif first_filter('date_bucket') and first_filter('date_bucket').val.lower() == 'year' %}
      {% if filter_lower_boundary('created_at') and filter_upper_boundary('created_at') %}
        generate_series(
          DATE_TRUNC('year', '{{ filter_lower_boundary('created_at') }}'::timestamp),
          DATE_TRUNC('year', '{{ filter_upper_boundary('created_at') }}'::timestamp),
          '1 year'::interval
        ) boundary
      {% elif filter_lower_boundary('created_at') %}
        generate_series(
          DATE_TRUNC('year', '{{ filter_lower_boundary('created_at') }}'::timestamp),
          DATE_TRUNC('year', NOW()),
          '1 year'::interval
        ) boundary
      {% elif filter_upper_boundary('created_at') %}
        generate_series(
          DATE_TRUNC('year', min_created_at),
          DATE_TRUNC('year', '{{ filter_upper_boundary('created_at') }}'::timestamp),
          '1 year'::interval
        ) boundary
      {% else %}
        generate_series(
          DATE_TRUNC('year', min_created_at),
          DATE_TRUNC('year', NOW()),
          '1 year'::interval
        ) boundary
      {% endif %}

    {% else %}
      {% if filter_lower_boundary('created_at') and filter_upper_boundary('created_at') %}
        generate_series(
          DATE_TRUNC('month', '{{ filter_lower_boundary('created_at') }}'::timestamp),
          DATE_TRUNC('month', '{{ filter_upper_boundary('created_at') }}'::timestamp),
          '1 month'::interval
        ) boundary
      {% elif filter_lower_boundary('created_at') %}
        generate_series(
          DATE_TRUNC('month', '{{ filter_lower_boundary('created_at') }}'::timestamp),
          DATE_TRUNC('month', NOW()),
          '1 month'::interval
        ) boundary
      {% elif filter_upper_boundary('created_at') %}
        generate_series(
          DATE_TRUNC('month', min_created_at),
          DATE_TRUNC('month', '{{ filter_upper_boundary('created_at') }}'::timestamp),
          '1 month'::interval
        ) boundary
      {% else %}
        generate_series(
          DATE_TRUNC('month', min_created_at),
          DATE_TRUNC('month', NOW()),
          '1 month'::interval
        ) boundary
      {% endif %}
    {% endif %}
  FROM pipeline_tracking_dates
),

-- Generate lower_boundary and upper_boundary for each bucket
pipeline_tracking_bucket_boundary AS (
  SELECT
    pipeline_id,
    boundary lower_boundary,
    COALESCE(
      LEAD(boundary) OVER (PARTITION BY pipeline_id ORDER BY boundary),
      {% if first_filter('date_bucket') and first_filter('date_bucket').val.lower() == 'week' %}
        boundary + INTERVAL '1 week'
      {% elif first_filter('date_bucket') and first_filter('date_bucket').val.lower() == 'quarter' %}
        boundary + INTERVAL '3 months'
      {% elif first_filter('date_bucket') and first_filter('date_bucket').val.lower() == 'year' %}
        boundary + INTERVAL '1 year'
      {% else %}
        boundary + INTERVAL '1 month'
      {% endif %}
    ) upper_boundary
  FROM pipeline_tracking_bucket_dates
),

-- Get the all pipeline tracking data before each bucket
pipeline_bucket_stage AS (
  SELECT
    pb.pipeline_id,
    pb.lower_boundary,
    pb.upper_boundary,
    FIRST_VALUE(pt.id) OVER w AS tracking_id,
    FIRST_VALUE(pt.to_stage_id) OVER w AS pipeline_stage_id,
    FIRST_VALUE(pt.to_stage_name) OVER w AS pipeline_stage_name,
    FIRST_VALUE(pt.to_stage_rank) OVER w AS pipeline_stage_rank,
    FIRST_VALUE(pt.created_at) OVER w AS tracking_created_at
  FROM pipeline_tracking_bucket_boundary pb
  LEFT JOIN pipeline_map_tracking pt
    ON pt.pipeline_id = pb.pipeline_id
    AND pt.created_at < pb.upper_boundary
  WINDOW w AS (
    PARTITION BY pb.pipeline_id, pb.lower_boundary, pb.upper_boundary
    ORDER BY pt.created_at DESC
  )
),

-- Get pipeline stage count for each bucket
pipeline_bucket_stage_count AS (
  SELECT
    pbs.lower_boundary truncated_date,
    {% if first_filter('date_bucket') and first_filter('date_bucket').val.lower() == 'week' %}
      'week' AS date_bucket,
    {% elif first_filter('date_bucket') and first_filter('date_bucket').val.lower() == 'quarter' %}
      'quarter' AS date_bucket,
    {% elif first_filter('date_bucket') and first_filter('date_bucket').val.lower() == 'year' %}
      'year' AS date_bucket,
    {% else %}
      'month' AS date_bucket,
    {% endif %}
    pbs.pipeline_stage_id,
    pbs.pipeline_stage_name,
    pbs.pipeline_stage_rank,
    COUNT(DISTINCT pipeline_id) AS pipeline_count
  FROM pipeline_bucket_stage pbs
  JOIN select_list_value_outcome slvo
    ON slvo.id = pbs.pipeline_stage_id
  WHERE slvo.outcome_state IS NULL
    AND slvo.deleted_at IS NULL
    {{ and_filter_values('organization_id', col_alias='slvo.organization_id') }}
    {{ and_filter_values('pipeline_stage_id', col_alias='pbs.pipeline_stage_id') }}
    {{ and_filter_values('pipeline_stage_name', col_alias='pbs.pipeline_stage_name') }}
  GROUP BY 1, 2, 3, 4, 5
  ORDER BY
    pbs.lower_boundary,
    pbs.pipeline_stage_rank
)

SELECT * FROM pipeline_bucket_stage_count
