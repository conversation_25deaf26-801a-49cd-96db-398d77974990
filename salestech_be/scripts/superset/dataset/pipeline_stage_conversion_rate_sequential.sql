-- Example

-- sequential_stages
--            organization_id            |            stage             | outcome_state | rank |          prev_stage          |          next_stage
-- --------------------------------------+------------------------------+---------------+------+------------------------------+------------------------------
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | 1Nurturing123123123          |               |    0 |                              | 3qq123
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | 3qq123                       |               |   10 | 1Nurturing123123123          | 4q1234n
--  ...
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | new deal                     |               |  120 | Contact Out                  | sy test
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | sy test                      |               |  130 | new deal                     | CW
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | CW                           | CLOSED_WON    |  140 | sy test                      | CWW
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | CWW                          | CLOSED_WON    |  160 | CW                           | CWWW
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | CWWW                         | CLOSED_WON    |  180 | CWW                          | Xin's exclusive closed won 1
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | Xin's exclusive closed won 1 | CLOSED_WON    |  200 | CWWW                         | Xin's exclusive closed won 2
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | Xin's exclusive closed won 2 | CLOSED_WON    |  210 | Xin's exclusive closed won 1 |

-- map_sequential_stages
--            organization_id            |        stage        |     prev_stage      |  next_stage  | outcome_state | rank
-- --------------------------------------+---------------------+---------------------+--------------+---------------+------
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | 1Nurturing123123123 |                     | 3qq123       |               |    0
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | 3qq123              | 1Nurturing123123123 | 4q1234n      |               |   10
--  ...
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | new deal            | Contact Out         | sy test      |               |  120
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | sy test             | new deal            | Closed Won   |               |  130
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | Closed Won          | sy test             | Closed Won   | CLOSED_WON    |  140
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | Closed Won          | Closed Won          | Closed Won   | CLOSED_WON    |  160
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | Closed Won          | Closed Won          | Closed Won   | CLOSED_WON    |  180
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | Closed Won          | Closed Won          | Closed Won   | CLOSED_WON    |  200
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | Closed Won          | Closed Won          |              | CLOSED_WON    |  210

-- filtered_map_sequential_stages
--            organization_id            |        stage        |     prev_stage      |  next_stage  | outcome_state | rank
-- --------------------------------------+---------------------+---------------------+--------------+---------------+------
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | 1Nurturing123123123 |                     | 3qq123       |               |    0
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | 3qq123              | 1Nurturing123123123 | 4q1234n      |               |   10
--  ...
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | new deal            | Contact Out         | sy test      |               |  120
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | sy test             | new deal            | Closed Won   |               |  130
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | Closed Won          | sy test             |              | CLOSED_WON    |  140

-- pipeline_map_stage
--            organization_id            |               from_id                |  from_name  |                to_id                 |       to_name
-- --------------------------------------+--------------------------------------+-------------+--------------------------------------+---------------------
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | 0960ef6c-4224-4d0e-9e02-324c8a752c8d | middle      | eba7e9ac-b5a8-4088-b9d7-e6902ee1c806 | 1Nurturing123123123
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | aff67e42-5346-4ef7-8600-e4e6b5123b96 | Contact Out | aff67e42-5346-4ef7-8600-e4e6b5123b96 | Contact Out
--  ...
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | 1815d899-7b43-404c-aa75-8ffeae5f9ea3 | new deal    | 1815d899-7b43-404c-aa75-8ffeae5f9ea3 | new deal
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | e5f70571-04a6-4cf3-89c6-90759ffbdf73 | sy test     | e5f70571-04a6-4cf3-89c6-90759ffbdf73 | sy test
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | 03661148-cace-43e7-baa6-ee491780b341 | CW          | 03661148-cace-43e7-baa6-ee491780b341 | Closed Won
--  cce7b290-8a08-4904-a6c7-2b6613877cf5 | ae9a3fd0-f42c-45d7-816a-e80f047fe115 | CWWW        | ae9a3fd0-f42c-45d7-816a-e80f047fe115 | Closed Won

-- sequential_stage_conversion, another org
--            organization_id            |    stage     |  next_stage  | entered_cnt | advanced_cnt | conversion_ratio
-- --------------------------------------+--------------+--------------+-------------+--------------+------------------
--  c6734584-b3f3-4608-b62e-a993f4703a8e | New          | Nurturing    |           4 |            4 |           100.00
--  c6734584-b3f3-4608-b62e-a993f4703a8e | Nurturing    | Meeting Set  |           4 |            3 |            75.00
--  c6734584-b3f3-4608-b62e-a993f4703a8e | Meeting Set  | Meeting Held |           3 |            3 |           100.00
--  c6734584-b3f3-4608-b62e-a993f4703a8e | Meeting Held | Negotiation  |           3 |            2 |            66.67
--  c6734584-b3f3-4608-b62e-a993f4703a8e | Negotiation  | Contract Out |           2 |            1 |            50.00
--  c6734584-b3f3-4608-b62e-a993f4703a8e | Contract Out | Closed Won   |           1 |            1 |           100.00
--  c6734584-b3f3-4608-b62e-a993f4703a8e | Closed Won   |              |           1 |            0 |             0.00

WITH

-- Get select list value with outcome_state for later use
select_list_value_outcome AS (
  SELECT slv.*,
  psslv.outcome_state
  FROM select_list_value slv
  JOIN pipeline_stage_select_list_value_metadata psslv on psslv.select_list_value_id = slv.id
  WHERE TRUE
  {{ and_filter_values('organization_id', col_alias='slv.organization_id') }}
  {{ and_filter_values('select_list_id', col_alias='slv.select_list_id') }}
),

-- Exclude CLOSED_LOST because conversion rate only account for non-closed/closed-won stages conversion
filtered_select_list_value AS (
  SELECT slv.*
  FROM select_list_value_outcome slv
  WHERE slv.deleted_at IS NULL AND (slv.outcome_state IS NULL OR slv.outcome_state != 'CLOSED_LOST')
),

-- Calculate all closed-won stages for later use
closed_won_stages AS (
  select *
  from select_list_value_outcome slv
  where slv.outcome_state = 'CLOSED_WON'
),

-- Make stage conversion/transition sequentially by its rank
-- This will serve as PET(Primary Entity Table) for final results
sequential_stages AS (
  SELECT
    display_value stage,
    outcome_state,
    rank,
    LAG(display_value) OVER (ORDER BY rank) as prev_stage,
    LEAD(display_value) OVER (ORDER BY rank) as next_stage
  FROM filtered_select_list_value
),

-- Map all closed-won stages into one virtual 'Closed Won' stage
map_sequential_stages AS (
  SELECT
    CASE WHEN EXISTS(SELECT 1 FROM closed_won_stages cws WHERE cws.display_value = stage) THEN 'Closed Won' ELSE stage END as stage,
    CASE WHEN EXISTS(SELECT 1 FROM closed_won_stages cws WHERE cws.display_value = prev_stage) THEN 'Closed Won' ELSE prev_stage END as prev_stage,
    CASE WHEN EXISTS(SELECT 1 FROM closed_won_stages cws WHERE cws.display_value = next_stage) THEN 'Closed Won' ELSE next_stage END as next_stage,
    outcome_state,
    rank
  FROM sequential_stages
),

-- Filter the duplication caused by the map step above
filtered_map_sequential_stages AS (
  SELECT
    stage,
    prev_stage,
    CASE WHEN stage = next_stage THEN NULL ELSE next_stage END AS next_stage,
    outcome_state,
    rank
  FROM map_sequential_stages
  WHERE prev_stage IS NULL OR stage != prev_stage
),

-- Generate the stage map for pipeline_tracking
-- Also map all closed-won stages to single 'Closed Won' stage, do not use to_id in this case
pipeline_map_stage AS (
  SELECT
    s1.id from_id,
    s1.display_value from_name,
    CASE
      WHEN s1.mapped_output_value_id IS NULL
      THEN s1.id
      ELSE s2.id
    END AS to_id,
    CASE
      WHEN s1.mapped_output_value_id IS NULL AND s1.outcome_state IS NULL THEN s1.display_value
      WHEN s1.mapped_output_value_id IS NOT NULL AND s2.outcome_state IS NULL THEN s2.display_value
      ELSE 'Closed Won'
    END AS to_name
  FROM select_list_value_outcome s1
  LEFT JOIN select_list_value_outcome s2 ON s1.mapped_output_value_id = s2.id
),

-- Filter pipeline_tracking by created_at using Jinja Template
-- Append mapped stage name
stage_transitions AS (
  SELECT
      pms1.to_name AS pipeline_from_stage_name,
      pms2.to_name AS pipeline_to_stage_name,
      t.*
    FROM pipeline_tracking t
    JOIN pipeline p ON p.id = t.pipeline_id
    LEFT JOIN pipeline_map_stage pms1 ON t.from_value_uuid = pms1.from_id
    LEFT JOIN pipeline_map_stage pms2 ON t.to_value_uuid = pms2.from_id
    WHERE
      t.field_name = 'stage_id'
      {{ and_filter_values('organization_id', col_alias='t.organization_id') }}
      {{ and_filter_values('owner_user_id', col_alias='p.owner_user_id') }}
      {{ and_filter_values('created_at', col_alias='p.created_at') }}
),

-- Filter pipeline_tracking to include only sequential stage moves
sequential_stage_transitions AS (
  SELECT
    st.pipeline_from_stage_name stage,
    st.pipeline_to_stage_name next_stage,
    COUNT(*) transition_count
  FROM stage_transitions st
  JOIN filtered_map_sequential_stages ss ON
    (st.pipeline_from_stage_name = ss.stage AND
    st.pipeline_to_stage_name = ss.next_stage OR
    st.pipeline_from_stage_name IS NULL AND
    ss.prev_stage IS NULL AND
    st.pipeline_to_stage_name = ss.stage)
  GROUP BY 1, 2
),

-- Calculate final results
sequential_stage_conversion AS (
  SELECT
    ss.stage,
    ss.next_stage,
    COALESCE(sst.transition_count, 0) AS entered_cnt,
    LEAD(COALESCE(sst.transition_count, 0), 1, 0) OVER (ORDER BY rank) AS advanced_cnt,
    CASE
      WHEN COALESCE(sst.transition_count, 0) = 0 THEN 0.00
      ELSE ROUND(LEAD(COALESCE(sst.transition_count, 0), 1, 0) OVER (ORDER BY rank) / COALESCE(sst.transition_count, 0)::numeric * 100, 2)
    END
    AS conversion_ratio
  FROM filtered_map_sequential_stages ss
  LEFT JOIN sequential_stage_transitions sst
    ON ss.stage = sst.next_stage
  ORDER BY rank
)

SELECT * FROM sequential_stage_conversion
