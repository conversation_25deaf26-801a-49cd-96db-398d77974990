WITH

flattened_pipeline AS (
  SELECT
    p.*,
    unnest_reason.closed_reason_select_list_value_id
  FROM pipeline AS p
  LEFT JOIN LATERAL (
    SELECT UNNEST(p.closed_reason_select_list_value_ids) AS closed_reason_select_list_value_id
  ) AS unnest_reason ON true
  WHERE TRUE
  {{ and_filter_values('organization_id', col_alias='p.organization_id') }}
  {{ and_filter_values('owner_user_id', col_alias='p.owner_user_id') }}
  {{ and_filter_values('closed_at', col_alias='p.closed_at') }}
),

enriched_pipeline AS (
  SELECT
    fp.id,
    fp.display_name as pipeline_display_name,
    fp.amount,
    fp.owner_user_id,
    COALESCE(NULLIF(TRIM(CONCAT_WS(' ', u.first_name, u.last_name)), ''), '') as owner_display_name,
    fp.status,
    fp.created_at,
    fp.closed_at,
    psslv.outcome_state,
    fp.closed_reason_select_list_value_id as direct_closed_reason_select_list_value_id,
    COALESCE(slv_closed_reason.mapped_output_value_id, fp.closed_reason_select_list_value_id) AS effective_closed_reason_select_list_value_id,
    CASE
      WHEN slv_closed_reason.mapped_output_value_id IS NOT NULL THEN (SELECT display_value FROM select_list_value WHERE id = slv_closed_reason.mapped_output_value_id)
      ELSE slv_closed_reason.display_value
    END AS effective_closed_reason_display_value,
    fp.stage_id as direct_stage_id,
    COALESCE(slv_stage.mapped_output_value_id, fp.stage_id) AS effective_stage_id,
    CASE
      WHEN slv_stage.mapped_output_value_id IS NOT NULL THEN (SELECT display_value FROM select_list_value WHERE id = slv_stage.mapped_output_value_id)
      ELSE slv_stage.display_value
    END AS effectvie_stage_display_value
  FROM flattened_pipeline AS fp
  LEFT JOIN select_list_value AS slv_closed_reason ON fp.closed_reason_select_list_value_id = slv_closed_reason.id
  LEFT JOIN select_list_value AS slv_stage ON fp.stage_id = slv_stage.id
  LEFT JOIN pipeline_stage_select_list_value_metadata AS psslv ON psslv.select_list_value_id = fp.stage_id
  LEFT JOIN public.user AS u ON u.id = fp.owner_user_id
  WHERE fp.archived_at IS NULL
)

SELECT
  effective_closed_reason_select_list_value_id,
  effective_closed_reason_display_value,
  COALESCE(SUM(amount), 0) AS total_amount,
  COUNT(id) AS total_count
FROM enriched_pipeline p
WHERE TRUE
{{ and_filter_values('outcome_state', col_alias='p.outcome_state') }}
GROUP BY
  effective_closed_reason_select_list_value_id,
  effective_closed_reason_display_value
ORDER BY
  total_amount DESC
