WITH

selected_enriched_pipeline_tracking AS (
  SELECT
    pt.pipeline_id,
    p.display_name,
    COALESCE(NULLIF(TRIM(CONCAT_WS(' ', u.first_name, u.last_name)), ''), '') as owner_display_name,
    pt.is_current,
    EXTRACT(EPOCH FROM (NOW() - pt.created_at)) / 86400 as duration_in_current_stage_days,
    COALESCE(slv_to_stage.mapped_output_value_id, pt.to_value_uuid) AS effective_stage_id,
    CASE
      WHEN slv_to_stage.mapped_output_value_id IS NOT NULL THEN (SELECT display_value FROM select_list_value WHERE id = slv_to_stage.mapped_output_value_id)
      ELSE slv_to_stage.display_value
    END AS effective_stage_display_value
  FROM pipeline_tracking AS pt
  JOIN pipeline AS p ON pt.pipeline_id = p.id AND pt.organization_id = p.organization_id
  LEFT JOIN select_list_value AS slv_to_stage ON pt.to_value_uuid = slv_to_stage.id AND pt.organization_id = slv_to_stage.organization_id
  LEFT JOIN public.user AS u ON u.id = p.owner_user_id
  WHERE p.archived_at IS NULL AND closed_at IS NULL AND pt.is_current IS TRUE AND pt.field_name = 'stage_id'
  {{ and_filter_values('organization_id', col_alias='pt.organization_id') }}
  {{ and_filter_values('owner_user_id', col_alias='p.owner_user_id') }}
),

median_duration_stats AS (
  SELECT
    effective_stage_id,
    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY duration_in_current_stage_days) AS median_duration_days
  FROM selected_enriched_pipeline_tracking
  GROUP BY effective_stage_id
),

pipeline_stage_transaction_age_median AS (
  SELECT
    sept.*,
    mds.median_duration_days,
    CASE
      WHEN sept.duration_in_current_stage_days > mds.median_duration_days THEN true
      ELSE false
    END AS is_top50
  FROM selected_enriched_pipeline_tracking AS sept
  LEFT JOIN median_duration_stats AS mds ON sept.effective_stage_id = mds.effective_stage_id
)

SELECT *
FROM pipeline_stage_transaction_age_median p
WHERE TRUE
{{ and_filter_values('median_duration_days', col_alias='p.median_duration_days') }}
{{ and_filter_values('is_top50', col_alias='p.is_top50') }}
