from temporalio import workflow
from temporalio.common import RetryPolicy
from temporalio.exceptions import ApplicationError

with workflow.unsafe.imports_passed_through():
    from datetime import timedelta
    from uuid import UUID

    from pydantic import BaseModel

    from salestech_be.db.models.prospecting_run import ProspectingRunStatus
    from salestech_be.integrations.temporal.config import (
        DEFAULT_TASK_MAX_RETRY,
        TemporalTaskQueue,
    )
    from salestech_be.ree_logging import get_logger
    from salestech_be.temporal.activities.prospecting.people_bulk_import import (
        add_contacts_to_domain_lists,
        add_contacts_to_sequence,
        convert_people_to_contacts,
        enrich_people_with_run,
        finish_prospecting_run,
    )

logger = get_logger(__name__)


class PeopleBulkImportWorkflowInput(BaseModel):
    user_id: UUID
    organization_id: UUID
    prospecting_run_id: UUID
    sequence_id: UUID | None = None
    domain_list_ids: list[UUID] | None = None


@workflow.defn
class PeopleBulkImportWorkflow:
    RETRY_POLICY: RetryPolicy = RetryPolicy(
        maximum_interval=timedelta(minutes=1),
        maximum_attempts=DEFAULT_TASK_MAX_RETRY,
        non_retryable_error_types=[
            "NotImplementedError",
            "ResourceNotFoundError",
            "IllegalStateError",
            "ServiceError",
            "InvalidArgumentError",
        ],
    )

    DEFAULT_ACTIVITY_TIMEOUT: timedelta = timedelta(minutes=30)
    DEFAULT_ACTIVITY_TIMEOUT_FOR_CONVERT_PEOPLE_TO_CONTACTS: timedelta = timedelta(
        minutes=60
    )

    @staticmethod
    def get_task_queue() -> str:
        return TemporalTaskQueue.PROSPECTING_TASK_QUEUE

    @workflow.run
    async def run(self, data: PeopleBulkImportWorkflowInput) -> None:
        logger.bind(
            prospecting_run_id=data.prospecting_run_id,
            organization_id=data.organization_id,
            user_id=data.user_id,
        ).info("Starting people bulk import workflow")
        try:
            # enrich_people_with_run
            enriched_people_ids = await workflow.execute_activity(
                enrich_people_with_run,
                args=[
                    data.user_id,
                    data.organization_id,
                    data.prospecting_run_id,
                ],
                start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=self.RETRY_POLICY,
            )
            if len(enriched_people_ids) == 0:
                logger.bind(
                    prospecting_run_id=data.prospecting_run_id,
                    organization_id=data.organization_id,
                    user_id=data.user_id,
                ).warning("No people to enrich")
            # convert_people_to_contacts
            contact_simple_list = []
            if len(enriched_people_ids) > 0:
                contact_simple_list = await workflow.execute_activity(
                    convert_people_to_contacts,
                    args=[
                        enriched_people_ids,
                        data.prospecting_run_id,
                        data.organization_id,
                        data.user_id,
                    ],
                    start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT_FOR_CONVERT_PEOPLE_TO_CONTACTS,
                    retry_policy=RetryPolicy(
                        maximum_attempts=1,  # to avoid duplicate credit usage
                    ),
                )
            # add_contacts_to_sequence
            if data.sequence_id and len(contact_simple_list) > 0:
                await workflow.execute_activity(
                    add_contacts_to_sequence,
                    args=[
                        contact_simple_list,
                        data.sequence_id,
                        data.prospecting_run_id,
                        data.organization_id,
                        data.user_id,
                    ],
                    start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                    retry_policy=self.RETRY_POLICY,
                )
            # add_contacts_to_domain_lists
            if data.domain_list_ids and len(contact_simple_list) > 0:
                await workflow.execute_activity(
                    add_contacts_to_domain_lists,
                    args=[
                        contact_simple_list,
                        data.domain_list_ids,
                        data.prospecting_run_id,
                        data.organization_id,
                        data.user_id,
                    ],
                    start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                    retry_policy=self.RETRY_POLICY,
                )
            # mark_run_as_completed
            await workflow.execute_activity(
                finish_prospecting_run,
                args=[
                    data.prospecting_run_id,
                    data.organization_id,
                    ProspectingRunStatus.COMPLETED,
                    None,
                ],
                start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=self.RETRY_POLICY,
            )
            logger.bind(
                prospecting_run_id=data.prospecting_run_id,
                organization_id=data.organization_id,
                user_id=data.user_id,
            ).info("People bulk import workflow completed")
        except Exception as e:
            await workflow.execute_activity(
                finish_prospecting_run,
                args=[
                    data.prospecting_run_id,
                    data.organization_id,
                    ProspectingRunStatus.FAILED,
                    str(e),
                ],
                start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=self.RETRY_POLICY,
            )
            logger.bind(
                prospecting_run_id=data.prospecting_run_id,
                organization_id=data.organization_id,
                user_id=data.user_id,
                exc_info=e,
            ).error("Prospecting run failed")
            raise ApplicationError(
                f"Prospecting run {data.prospecting_run_id} failed",
                non_retryable=True,
            )
