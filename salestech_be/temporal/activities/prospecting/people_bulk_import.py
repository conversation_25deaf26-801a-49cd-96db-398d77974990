from temporalio import activity, workflow

with workflow.unsafe.imports_passed_through():
    from uuid import UUID

    from salestech_be.db.dto.prospecting_dto import (
        ContactSimpleInfo,
    )
    from salestech_be.db.models.prospecting_run import (
        ProspectingRunStatus,
        ProspectingRunUpdate,
    )
    from salestech_be.ree_logging import get_logger
    from salestech_be.temporal.activity_decorator import with_tracing
    from salestech_be.temporal.database import get_or_init_db_engine
    from salestech_be.util.time import zoned_utc_now
    from salestech_be.web.api.prospecting.common.prospecting_common_service import (
        get_prospecting_common_service,
    )


logger = get_logger(__name__)


@activity.defn
@with_tracing
async def enrich_people_with_run(
    user_id: UUID,
    organization_id: UUID,
    prospecting_run_id: UUID,
) -> list[UUID]:
    """
    Optimized activity for enriching people with failure recovery capability.

    This activity now delegates the complex business logic to prospecting_common_service
    for better testability and reusability.
    """
    db_engine = await get_or_init_db_engine()
    prospecting_common_service = get_prospecting_common_service(db_engine)

    return await prospecting_common_service.enrich_people_with_run_recovery(
        user_id=user_id,
        organization_id=organization_id,
        prospecting_run_id=prospecting_run_id,
    )


@activity.defn
@with_tracing
async def convert_people_to_contacts(
    person_ids: list[UUID],
    prospecting_run_id: UUID,
    organization_id: UUID,
    user_id: UUID,
) -> list[ContactSimpleInfo]:
    db_engine = await get_or_init_db_engine()
    prospecting_common_service = get_prospecting_common_service(db_engine)

    person_dto_list = (
        await prospecting_common_service.find_person_dto_list_by_person_ids(
            person_ids=person_ids,
            organization_id=organization_id,
        )
    )

    return await prospecting_common_service.convert_people_to_contacts(
        person_dto_list=person_dto_list,
        organization_id=organization_id,
        user_id=user_id,
        prospecting_run_id=prospecting_run_id,
    )


@activity.defn
@with_tracing
async def add_contacts_to_sequence(
    contact_simple_list: list[ContactSimpleInfo],
    sequence_id: UUID,
    prospecting_run_id: UUID,
    organization_id: UUID,
    user_id: UUID,
) -> None:
    db_engine = await get_or_init_db_engine()
    prospecting_common_service = get_prospecting_common_service(db_engine)

    await prospecting_common_service.add_contacts_to_sequence(
        contact_simple_list=contact_simple_list,
        sequence_id=sequence_id,
        prospecting_run_id=prospecting_run_id,
        organization_id=organization_id,
        user_id=user_id,
    )


@activity.defn
@with_tracing
async def add_contacts_to_domain_lists(
    contact_simple_list: list[ContactSimpleInfo],
    domain_list_ids: list[UUID],
    prospecting_run_id: UUID,
    organization_id: UUID,
    user_id: UUID,
) -> None:
    db_engine = await get_or_init_db_engine()
    prospecting_common_service = get_prospecting_common_service(db_engine)

    await prospecting_common_service.add_contacts_to_domain_lists(
        contact_simple_list=contact_simple_list,
        domain_list_ids=domain_list_ids,
        prospecting_run_id=prospecting_run_id,
        organization_id=organization_id,
        user_id=user_id,
    )


@activity.defn
@with_tracing
async def finish_prospecting_run(
    prospecting_run_id: UUID,
    organization_id: UUID,
    status: ProspectingRunStatus,
    error_info: str | None = None,
) -> None:
    db_engine = await get_or_init_db_engine()
    prospecting_common_service = get_prospecting_common_service(db_engine)

    prospecting_run_update = ProspectingRunUpdate(
        status=status,
        ends_at=zoned_utc_now(),
    )
    if error_info:
        prospecting_run_update.error_info = error_info

    await prospecting_common_service.prospecting_run_service.update_run(
        prospecting_run_id=prospecting_run_id,
        organization_id=organization_id,
        prospecting_run_update=prospecting_run_update,
    )
