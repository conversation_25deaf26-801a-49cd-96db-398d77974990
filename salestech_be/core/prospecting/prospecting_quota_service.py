from datetime import datetime
from uuid import UUID, uuid4

from dateutil.relativedelta import relativedelta
from fastapi import Request

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import ErrorDetails
from salestech_be.common.exception.exception import PaymentError, ServiceError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.core.quota.service.quota_policy_service import (
    QuotaPolicyService,
    get_quota_policy_service_from_engine,
)
from salestech_be.core.quota.service.quota_service import (
    QuotaService,
    get_quota_service_by_db_engine,
)
from salestech_be.core.quota.type.quota_policy_type import (
    QuotaPolicy,
    UpdateQuotaPolicyRequest,
)
from salestech_be.core.user.service.user_query_service import (
    UserQueryService,
    get_user_query_service_by_db_engine,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.prospecting_dto import (
    PROSPECTING_DEFAULT_MONTHLY_COMPANY_SEARCH_QUOTA_LIMIT_PER_ORG,
    PROSPECTING_DEFAULT_MONTHLY_COMPANY_SEARCH_QUOTA_LIMIT_PER_SEAT,
    PROSPECTING_DEFAULT_MONTHLY_PEOPLE_ENRICHMENT_QUOTA_LIMIT_PER_ORG,
    PROSPECTING_DEFAULT_MONTHLY_PEOPLE_ENRICHMENT_QUOTA_LIMIT_PER_SEAT,
    PROSPECTING_DEFAULT_MONTHLY_PEOPLE_SEARCH_QUOTA_LIMIT_PER_ORG,
    PROSPECTING_DEFAULT_MONTHLY_PEOPLE_SEARCH_QUOTA_LIMIT_PER_SEAT,
)
from salestech_be.db.models.quota import (
    QuotaConsumerEntityType,
    QuotaConsumingResource,
    QuotaPeriod,
    QuotaUsage,
)
from salestech_be.db.models.quota import (
    QuotaPolicy as DbQuotaPolicy,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now

logger = get_logger(__name__)


# Mapping of resource types to their default organization-level quota limits
PER_ORG_PROSPECTING_QUOTA_LIMIT_MAP = {
    QuotaConsumingResource.PROSPECTING_PEOPLE_SEARCH: PROSPECTING_DEFAULT_MONTHLY_PEOPLE_SEARCH_QUOTA_LIMIT_PER_ORG,
    QuotaConsumingResource.PROSPECTING_COMPANY_SEARCH: PROSPECTING_DEFAULT_MONTHLY_COMPANY_SEARCH_QUOTA_LIMIT_PER_ORG,
    QuotaConsumingResource.PROSPECTING_ENRICHMENT: PROSPECTING_DEFAULT_MONTHLY_PEOPLE_ENRICHMENT_QUOTA_LIMIT_PER_ORG,
}

# Mapping of resource types to their default user-level quota limits
PER_USER_PROSPECTING_QUOTA_LIMIT_MAP = {
    QuotaConsumingResource.PROSPECTING_PEOPLE_SEARCH: PROSPECTING_DEFAULT_MONTHLY_PEOPLE_SEARCH_QUOTA_LIMIT_PER_SEAT,
    QuotaConsumingResource.PROSPECTING_COMPANY_SEARCH: PROSPECTING_DEFAULT_MONTHLY_COMPANY_SEARCH_QUOTA_LIMIT_PER_SEAT,
    QuotaConsumingResource.PROSPECTING_ENRICHMENT: PROSPECTING_DEFAULT_MONTHLY_PEOPLE_ENRICHMENT_QUOTA_LIMIT_PER_SEAT,
}


class ProspectingQuotaService:
    def __init__(
        self,
        user_query_service: UserQueryService,
        quota_service: QuotaService,
        quota_policy_service: QuotaPolicyService,
    ):
        self.user_query_service = user_query_service
        self.quota_service = quota_service
        self.quota_policy_service = quota_policy_service

    async def initialize_user_onboarding_quota(
        self,
        *,
        user_id: UUID,
        organization_id: UUID,
    ) -> None:
        # Process each prospecting resource type that requires quota management
        prospecting_resources = [
            QuotaConsumingResource.PROSPECTING_ENRICHMENT,
            QuotaConsumingResource.PROSPECTING_PEOPLE_SEARCH,
            QuotaConsumingResource.PROSPECTING_COMPANY_SEARCH,
        ]

        _logger = logger.bind(
            user_id=user_id,
            organization_id=organization_id,
        )
        _logger.info("Initializing quota policies for user onboarding")
        try:
            for resource in prospecting_resources:
                _logger.info(f"Processing quota policy for resource: {resource}")
                await self._handle_resource_quota_policy(
                    resource=resource, user_id=user_id, organization_id=organization_id
                )
            _logger.info(
                "Successfully initialized all quota policies for user onboarding"
            )
        except Exception as e:
            # Don't raise an error here, just log it to avoid breaking the onboarding process
            _logger.bind(
                exc_info=e,
            ).error("Failed to initialize quota policies for user onboarding")

    # Helper method to get or create an active policy
    async def _get_or_create_active_policy(
        self,
        organization_id: UUID,
        user_id: UUID,
        resource: QuotaConsumingResource,
    ) -> QuotaPolicy:
        # Get policies for this resource
        quota_policies = await self.quota_policy_service.list_quota_policies_by_entity_ids_and_type_resource(
            entity_id_list=[organization_id],
            resource=resource,
            entity_type=QuotaConsumerEntityType.ORGANIZATION,
            organization_id=organization_id,
        )

        # Separate active and expired policies
        active_policies = [p for p in quota_policies if not p.is_expired]
        expired_policies = [p for p in quota_policies if p.is_expired]

        _logger = logger.bind(
            organization_id=organization_id,
            user_id=user_id,
            resource=resource,
        )

        # Handle based on active policies count
        if not active_policies:
            # No active policies found, create one from expired policies or current time
            _logger.info("No active policies found, creating new policy")
            return await self._renew_expired_policies(
                organization_id=organization_id,
                user_id=user_id,
                resource=resource,
                expired_policies=expired_policies,
            )
        elif len(active_policies) > 1:
            # Multiple active policies - error state
            _logger.error(
                f"Found multiple active policies ({len(active_policies)}) - invalid state"
            )
            raise ServiceError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.MULTIPLE_QUOTA_POLICIES,
                    details=f"Multiple active quota policies found for organization {organization_id} and resource {resource}",
                )
            )
        else:
            # One active policy found
            _logger.info(f"Found one active policy with ID: {active_policies[0].id}")
            return active_policies[0]

    async def _handle_resource_quota_policy(
        self,
        resource: QuotaConsumingResource,
        user_id: UUID,
        organization_id: UUID,
    ) -> None:
        # Get active policy for this resource
        policy = await self._get_or_create_active_policy(
            organization_id=organization_id,
            user_id=user_id,
            resource=resource,
        )

        # Handle onboarding-specific update logic
        _logger = logger.bind(
            user_id=user_id,
            organization_id=organization_id,
            resource=resource,
            policy_id=policy.id,
        )

        # Check if we need to increase the quota limit for this policy
        current_limit = policy.quota_limit
        new_limit = min(
            current_limit + PER_USER_PROSPECTING_QUOTA_LIMIT_MAP[resource],
            PER_ORG_PROSPECTING_QUOTA_LIMIT_MAP[resource],
        )

        # Update policy quota limit if needed
        if current_limit < new_limit:
            _logger.info(f"Increasing quota limit from {current_limit} to {new_limit}")
            await self.quota_policy_service.update_quota_policy(
                user_id=user_id,
                policy_id=policy.id,
                organization_id=organization_id,
                api_request=UpdateQuotaPolicyRequest(
                    quota_limit=new_limit,
                ),
            )
        else:
            _logger.info(
                f"No quota limit increase needed, current: {current_limit}, max: {PER_ORG_PROSPECTING_QUOTA_LIMIT_MAP[resource]}"
            )

    async def _calculate_org_quota_limit(
        self,
        organization_id: UUID,
        resource: QuotaConsumingResource,
    ) -> int:
        # Get the list of active users in the organization
        user_orgs = await self.user_query_service.list_org_users_by_organization_id(
            organization_id=organization_id,
            active_users_only=True,
        )
        if not user_orgs:
            return 0

        # Calculate quota based on user count, not exceeding the org-level limit
        return min(
            len(user_orgs) * PER_USER_PROSPECTING_QUOTA_LIMIT_MAP[resource],
            PER_ORG_PROSPECTING_QUOTA_LIMIT_MAP[resource],
        )

    def _calculate_billing_anchor_date(
        self, original_date: datetime, current_date: datetime
    ) -> datetime:
        # Calculate the appropriate billing anchor date based on the original policy date.
        # Always use the current month's date regardless of whether current_date has already
        # passed the target day in the current month.

        # Get the last day of current month to check validity
        last_day_of_current_month = (
            current_date.replace(day=1) + relativedelta(months=1, days=-1)
        ).day
        target_day = min(original_date.day, last_day_of_current_month)

        # Always use the date in the current month, even if it's in the past
        # This is to maintain consistency with billing cycles
        return current_date.replace(day=target_day)

    async def _create_new_quota_policy(
        self,
        resource: QuotaConsumingResource,
        user_id: UUID,
        organization_id: UUID,
        quota_limit: int,
        created_at: datetime | None = None,
    ) -> DbQuotaPolicy:
        # Create a new quota policy with the specified parameters
        return await self.quota_policy_service.quota_policy_repo.insert(
            DbQuotaPolicy(
                id=uuid4(),
                quota_limit=quota_limit,
                period=QuotaPeriod.MONTHLY,
                resource=resource,
                entity_type=QuotaConsumerEntityType.ORGANIZATION,
                entity_id=organization_id,
                applied_sub_entity_types=None,
                created_at=created_at if created_at else zoned_utc_now(),
                created_by_user_id=user_id,
                organization_id=organization_id,
            )
        )

    async def _renew_expired_policies(
        self,
        organization_id: UUID,
        user_id: UUID,
        resource: QuotaConsumingResource,
        expired_policies: list[QuotaPolicy],
    ) -> QuotaPolicy:
        if expired_policies:
            # Find the most recent expired policy to use as a template
            expired_policies.sort(key=lambda x: x.created_at)
            near_policy = expired_policies[-1]

            # Calculate billing anchor based on the most recent expired policy
            # handling edge cases like Feb 29, 30, 31
            _now = zoned_utc_now()
            billing_anchor = self._calculate_billing_anchor_date(
                original_date=near_policy.created_at, current_date=_now
            )
        else:
            # No expired policies, use current time as billing anchor
            billing_anchor = zoned_utc_now()

        # Calculate new quota limit based on current organization state
        quota_limit = await self._calculate_org_quota_limit(
            organization_id=organization_id,
            resource=resource,
        )

        # Create new policy with appropriate billing anchor
        db_policy = await self._create_new_quota_policy(
            resource=resource,
            user_id=user_id,
            organization_id=organization_id,
            quota_limit=quota_limit,
            created_at=billing_anchor,
        )
        return QuotaPolicy.from_db(db_policy)

    async def check_quota_limit_exceeded(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        resource: QuotaConsumingResource,
        estimated_usage: int,
    ) -> None:
        # Set up logger with context
        _logger = logger.bind(
            organization_id=organization_id,
            user_id=user_id,
            resource=resource,
            estimated_usage=estimated_usage,
        )
        _logger.info("Checking quota limit")

        # Get active policy for this resource
        active_policy = await self._get_or_create_active_policy(
            organization_id=organization_id,
            user_id=user_id,
            resource=resource,
        )
        _logger.info(
            f"Using policy {active_policy.id} with limit {active_policy.quota_limit}"
        )

        # Calculate the usage period based on policy creation date
        start_time = active_policy.created_at
        end_time = start_time + relativedelta(months=1)

        # Get current usage for the policy period
        used_quota = await self.quota_service.get_aggregate_user_usage_in_period(
            organization_id=organization_id,
            entity_id=organization_id,
            entity_type=QuotaConsumerEntityType.ORGANIZATION,
            resource=resource,
            period_start=start_time,
            period_end=end_time,
        )

        _logger.info(
            f"Current usage: {used_quota}, Estimated additional usage: {estimated_usage}, Limit: {active_policy.quota_limit}"
        )

        # Check if usage would exceed limit
        if used_quota + estimated_usage > active_policy.quota_limit:
            _logger.error(
                f"Quota limit exceeded for {resource}. Used: {used_quota}, "
                f"Requested: {estimated_usage}, Limit: {active_policy.quota_limit}"
            )
            raise PaymentError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.QUOTA_LIMIT_EXCEEDED,
                    details=f"Organization has exceeded quota for {resource}. "
                    f"Used: {used_quota}, Limit: {active_policy.quota_limit}",
                )
            )

        _logger.info("Quota check passed, sufficient quota available")

    async def insert_quota_usage(
        self,
        organization_id: UUID,
        user_id: UUID,
        resource: QuotaConsumingResource,
        usage: int,
        applied_sub_resource: QuotaConsumingResource | None = None,
    ) -> QuotaUsage:
        # Set up logger with context
        _logger = logger.bind(
            organization_id=organization_id,
            resource=resource,
            usage=usage,
            user_id=user_id,
        )

        _logger.info("Recording quota usage")
        # Insert the usage record in the database
        quota_usage = await self.quota_service.insert_new_usage(
            organization_id=organization_id,
            entity_id=organization_id,
            entity_type=QuotaConsumerEntityType.ORGANIZATION,
            resource=resource,
            usage=usage,
            hour_start=zoned_utc_now(),
            applied_sub_resource=applied_sub_resource,
            created_by_user_id=user_id,
        )
        _logger.info("Successfully recorded quota usage")
        return quota_usage


class SingletonProspectingQuotaService(Singleton, ProspectingQuotaService):
    """Singleton implementation of ProspectingQuotaService for efficient reuse."""


def get_prospecting_quota_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> ProspectingQuotaService:
    return SingletonProspectingQuotaService(
        user_query_service=get_user_query_service_by_db_engine(db_engine=db_engine),
        quota_service=get_quota_service_by_db_engine(db_engine=db_engine),
        quota_policy_service=get_quota_policy_service_from_engine(engine=db_engine),
    )


def get_prospecting_quota_service(
    request: Request,
) -> ProspectingQuotaService:
    db_engine = get_db_engine(request=request)
    return get_prospecting_quota_service_by_db_engine(db_engine=db_engine)
