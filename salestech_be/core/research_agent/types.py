from __future__ import annotations

import time
import uuid
from decimal import Decimal
from enum import StrEnum
from http import HTT<PERSON>tatus
from uuid import UUID

from pydantic import BaseModel, Field, field_validator

from salestech_be.common.ree_llm import LLMTraceMetadata
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime

# This file contains the context models for the research agent.


# Track the time of research workflow's lifecycles.
class ResearchTime(BaseModel):
    # cdc_triggered_at is the time when the research task was triggered by CDC
    cdc_triggered_at: float
    # created_at is the time when the company intel was created
    intel_created_at: float | None = None
    # wf_started_at is the time when the workflow was started
    wf_started_at: float = Field(default_factory=lambda: time.time_ns())

    @field_validator("cdc_triggered_at", mode="before")
    @classmethod
    def validate_cdc_triggered_at(cls, value: int | float) -> float:
        """Convert int to float for cdc_triggered_at field."""
        return float(value)

    @field_validator("intel_created_at", mode="before")
    @classmethod
    def validate_intel_created_at(cls, value: int | float) -> float | None:
        """Convert int to float for intel_created_at field."""
        if value is None:
            return None
        return float(value)

    @field_validator("wf_started_at", mode="before")
    @classmethod
    def validate_wf_started_at(cls, value: int | float) -> float:
        """Convert int to float for wf_started_at field."""
        return float(value)

    @property
    def research_duration(self) -> float:
        """The duration since the research task was triggered by CDC."""
        return time.time_ns() - self.cdc_triggered_at

    @property
    def research_duration_since_first_time(self) -> float:
        """The duration since the first time the research task was triggered by CDC."""
        if not self.intel_created_at:
            return 0
        return time.time_ns() - self.intel_created_at

    @property
    def research_duration_since_epoch(self) -> float:
        """The duration since the workflow was started."""
        return time.time_ns() - self.wf_started_at


class ResearchContextType(StrEnum):
    COMPANY = "company"
    PERSON = "person"


# Research Context, mainly used for Langfuse tracing
class ResearchContext(BaseModel):
    research_type: ResearchContextType
    research_time: ResearchTime
    session_id: str
    user_id: UUID | None = None
    tags: list[str] | None = None

    def get_session_id(self) -> str:
        """Get the session ID for this research context.

        Returns:
            str: If session_id is set, returns that value (e.g. `research:company:{session_id}`).
                Otherwise generates and returns a fallback session ID in the format
                `research:{research_type}:adhoc:{uuid}`.
        """
        if self.session_id:
            return self.session_id
        # otherwise fall back to adhoc session ID
        return f"research:{self.research_type.value}:adhoc:{uuid.uuid4()}"

    def get_user_id(self) -> UUID | None:
        return self.user_id

    def get_tags(self) -> list[str]:
        return self.tags or []

    def to_litellm_metadata(self, trace_name: str) -> LLMTraceMetadata:
        """Convert research context to LLM tracing metadata.

        This method creates tracing metadata that can be sent to tracing services like Langfuse
        to track and analyze LLM interactions.

        Args:
            trace_name: `Name` on Langfuse/Traces. Type of prompt. e.g. `company_news.news_with_window_grounding`.

        Returns:
            LLMTraceMetadata containing the trace name, session ID, user ID and tags
            for tracing this research context
        """
        return LLMTraceMetadata(
            trace_name=trace_name,
            session_id=self.get_session_id(),
            user_id=self.get_user_id(),
            tags=self.get_tags(),
        )


class IntelProviderTypeEnum(StrEnum):
    """The data provider for research"""

    CRUSTDATA = "crustdata"
    BRIGHTDATA = "brightdata"
    GEMINI = "gemini"
    SCRAPINGDOG = "scrapingdog"

    @classmethod
    def from_str(cls, name: str) -> IntelProviderTypeEnum:
        try:
            return cls(name.lower())
        except ValueError:
            raise ValueError(f"Invalid IntelProviderTypeEnum: {name}")


class IntelProviderBaseModel(BaseModel):
    provider: IntelProviderTypeEnum | None = None


class ResearchView(StrEnum):
    """FE view page for account research"""

    MEETINGS = "meetings"
    ACCOUNTS = "accounts"
    OPPORTUNITIES = "opportunities"
    CONTACTS = "contacts"
    UNKNOWN = "unknown"

    @classmethod
    def from_referer(
        cls,
        referer: str | None,
    ) -> ResearchView | None:  # Use forward reference string
        if referer and "meetings" in referer:
            return ResearchView.MEETINGS
        elif referer and "accounts" in referer:
            return ResearchView.ACCOUNTS
        elif referer and "pipelines" in referer:
            return ResearchView.OPPORTUNITIES
        elif referer and "contacts" in referer:
            return ResearchView.CONTACTS
        elif referer is not None:
            return ResearchView.UNKNOWN
        else:
            return None


class ResearchDataSourceType(StrEnum):
    LINKEDIN = "linkedin"
    DOMAIN = "domain"


class ResearchDataSourceKey(BaseModel):
    # value is either a linkedin url or a domain depending on the data_source_type
    value: str
    data_source_type: ResearchDataSourceType


class SaveIntelActivityResult(StrEnum):
    DUPLICATE = "duplicate"
    SAVED = "saved"


class ResearchType(StrEnum):
    COMPANY = "company"
    PERSON = "person"


# The status of the research
class ResearchStatus(StrEnum):
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"


# The type of input to get the IntelCompanyInfo and IntelPersonInfo
class IntelInputType(StrEnum):
    DOMAIN = "domain"
    BUSINESS_EMAIL = "business_email"
    LINKEDIN_URL = "linkedin_url"


# The origin where we get the linkedin url
class IntelLinkedinUrlSource(StrEnum):
    USER_INPUT = "user_input"
    CRUSTDATA = "crustdata"
    SCRAPINGDOG = "scrapingdog"


class DBAccountAISuggestedValues(BaseModel):
    estimated_revenue_higher_bound_usd: Decimal | None = None
    employee_count_range: str | None = None
    linkedin_company_description: str | None = None
    linkedin_industries: list[str] | None = None
    updated_at: ZoneRequiredDateTime | None = None
    company_website: str | None = None


class DBContactAISuggestedValues(BaseModel):
    linkedin_profile_url: str | None = None
    title: str | None = None
    name: str | None = None
    updated_at: ZoneRequiredDateTime | None = None


RESEARCH_AGENT_CLIENT_RECOVERABLE_ERRORS = [
    HTTPStatus.UNAUTHORIZED,
    HTTPStatus.FORBIDDEN,
    HTTPStatus.TOO_MANY_REQUESTS,
    HTTPStatus.INTERNAL_SERVER_ERROR,
    HTTPStatus.BAD_GATEWAY,
    HTTPStatus.SERVICE_UNAVAILABLE,
    HTTPStatus.GATEWAY_TIMEOUT,
]
