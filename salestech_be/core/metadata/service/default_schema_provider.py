from collections.abc import Mapping
from functools import cached_property
from typing import Final

from frozendict import frozendict

from salestech_be.common.exception import IllegalStateError
from salestech_be.common.type.metadata.common import (
    ObjectIdentifier,
    StandardObjectIdentifier,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    Dict<PERSON>ieldProperty,
    ListFieldProperty,
    NestedObjectFieldProperty,
)
from salestech_be.common.type.metadata.schema import (
    Relationship,
    StandardObjectDescriptor,
)
from salestech_be.core.account.types_v2 import (
    AccountAISuggestedAnnualRevenue,
    AccountAISuggestedCategoryList,
    AccountAISuggestedDescription,
    AccountAISuggestedEmployeeCount,
    AccountAISuggestedOfficialWebsite,
    AccountV2,
)
from salestech_be.core.activity.types import Activity, ActivitySubReference
from salestech_be.core.chat.types import Chat, ChatMessage
from salestech_be.core.citation.types import Citation, CitationForGiantTask
from salestech_be.core.comment.types import Comment
from salestech_be.core.common.types import (
    Address,
    BaseAISuggestedValue,
    DomainModel,
    MeetingBotStatusEvent,
    MeetingParticipant,
    UserOrganizationProfile,
)
from salestech_be.core.contact.types_v2 import (
    ContactAISuggestedDisplayName,
    ContactAISuggestedLinkedinUrl,
    ContactAISuggestedTitle,
    ContactForGiantTask,
    ContactV2,
)
from salestech_be.core.domain_object_list.types import (
    DomainObjectList,
    DomainObjectListItem,
)
from salestech_be.core.email.account.types import EmailAccountV2
from salestech_be.core.email.global_email.attachment_details_type_v2 import (
    AttachmentDetailsV2,
)
from salestech_be.core.email.global_email.email_participant_type_v2 import (
    EmailParticipantV2,
)
from salestech_be.core.email.global_email.global_message_type import GlobalMessage
from salestech_be.core.email.global_email.global_thread_type import (
    EmailThreadForGiantTask,
    GlobalThread,
)
from salestech_be.core.email.global_email.types_v2 import (
    GlobalMessageErrorInfoV2,
    GlobalMessageEventSummaryV2,
)
from salestech_be.core.email.outbound_domain.types_v2 import (
    DomainConfigurationModel,
    OutboundDomainV2,
)
from salestech_be.core.email.template.types import EmailTemplate
from salestech_be.core.goal.types import UserGoal
from salestech_be.core.meeting.types.meeting_screen_share_ranges import (
    MeetingScreenShareRanges,
)
from salestech_be.core.meeting.types.meeting_types_v2 import BotError, MeetingV2
from salestech_be.core.metadata.types import (
    ContactAccountAssociationLite,
    ContactAccountEmail,
    ContactAccountRole,
    ContactEmailLite,
    ContactPhoneNumber,
    ContactPipelineRole,
    PipelineStageSelectListValueLite,
    SelectListValueLite,
)
from salestech_be.core.pipeline.qualification_properties.competition import (
    Competition,
    Competitor,
)
from salestech_be.core.pipeline.qualification_properties.decision_criteria import (
    DecisionCriteria,
    DecisionCriteriaItem,
)
from salestech_be.core.pipeline.qualification_properties.decision_process import (
    DecisionProcess,
    DecisionProcessItem,
)
from salestech_be.core.pipeline.qualification_properties.identified_pain import (
    IdentifiedPain,
    IdentifiedPainItem,
)
from salestech_be.core.pipeline.qualification_properties.metric import (
    Metric,
    MetricItem,
)
from salestech_be.core.pipeline.qualification_properties.paper_process import (
    PaperProcess,
    PaperProcessItem,
)
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.core.prospecting.type.company_type_v2 import ProspectingCompany
from salestech_be.core.prospecting.type.credit import ProspectingCreditUsagePointV2
from salestech_be.core.prospecting.type.filter_field import (
    FilterFieldOptionsFacetV2 as ProspectingFilterFieldOptionsFacetV2,
)
from salestech_be.core.prospecting.type.filter_field import (
    FilterFieldOptionsV2 as ProspectingFilterFieldOptionsV2,
)
from salestech_be.core.prospecting.type.person_type_v2 import (
    ProspectingPerson,
    ProspectingPhoneNumberDetail,
)
from salestech_be.core.prospecting.type.query import ProspectingSavedSearchQueryV2
from salestech_be.core.prospecting.type.run import (
    PeopleEnrichmentSummary,
    ProspectingCreditByType,
    ProspectingCreditUsage,
    ProspectingListEnrollmentResult,
    ProspectingRunV2,
)
from salestech_be.core.prospecting.type.run import (
    RunResultV2 as ProspectingRunResultV2,
)
from salestech_be.core.schedule.types import EventSchedule
from salestech_be.core.sequence.type.sequence_enrollment_contact_type import (
    SequenceEnrollmentContact,
)
from salestech_be.core.sequence.type.sequence_enrollment_run_type import (
    SequenceEnrollmentRun,
)
from salestech_be.core.sequence.type.sequence_enrollment_types import SequenceEnrollment
from salestech_be.core.sequence.type.sequence_participant_type import (
    SequenceParticipant,
)
from salestech_be.core.sequence.type.sequence_schedule_type import (
    SequenceSchedule,
    SequenceScheduleTime,
)
from salestech_be.core.sequence.type.sequence_stats_type import SequenceStats
from salestech_be.core.sequence.type.sequence_step_execution_types import (
    SequenceStepExecution,
)
from salestech_be.core.sequence.type.sequence_step_types import SequenceStepV2
from salestech_be.core.sequence.type.sequence_step_variant_types import (
    SequenceStepVariantV2,
)
from salestech_be.core.sequence.type.sequence_v2 import SequenceV2
from salestech_be.core.task.giant_task_types import GiantTask
from salestech_be.core.task.types_v2 import TaskV2
from salestech_be.core.user.signature.types_v2 import SignatureV2
from salestech_be.core.user.types_v2 import OrganizationUserV2
from salestech_be.ree_logging import get_logger

logger = get_logger()

__all__ = [
    "DomainDefaultSchemaProviderV2",
    "system_default_domain_schema_provider_v2",
]


class DomainDefaultSchemaProviderV2:
    def __init__(self, domain_models: set[type[DomainModel]]):
        super().__init__()
        self._domain_models = domain_models
        self._default_object_descriptor_by_model: frozendict[
            type[DomainModel], StandardObjectDescriptor
        ] = self._validate_default_object_descriptors(domain_models=domain_models)
        self._default_object_descriptors_by_id: frozendict[
            StandardObjectIdentifier, StandardObjectDescriptor
        ] = frozendict(
            {
                object_descriptor.object_identifier: object_descriptor
                for object_descriptor in self._default_object_descriptor_by_model.values()
            }
        )
        self._default_object_model_by_id: frozendict[
            StandardObjectIdentifier, type[DomainModel]
        ] = self._validate_default_object_model_by_id(
            default_object_descriptor_by_model=self._default_object_descriptor_by_model
        )

    @cached_property
    def _all_standard_object_descriptors(self) -> tuple[StandardObjectDescriptor, ...]:
        object_descriptors = [*self._default_object_descriptor_by_model.values()]
        object_descriptors.sort(key=lambda x: x.object_identifier.object_name)
        return tuple(object_descriptors)

    @cached_property
    def _all_standard_object_descriptors_ignore_nested_object_field(
        self,
    ) -> tuple[StandardObjectDescriptor, ...]:
        _all_standard_object_descriptors = self._all_standard_object_descriptors
        _nested_object_field_descriptors: set[ObjectIdentifier] = set()
        for _object_descriptor in _all_standard_object_descriptors:
            for _field in _object_descriptor.fields:
                if isinstance(_field.field_type_property, NestedObjectFieldProperty):
                    _nested_object_field_descriptors.add(
                        _field.field_type_property.object_identifier
                    )
        return tuple(
            _object_descriptor
            for _object_descriptor in _all_standard_object_descriptors
            if _object_descriptor.object_identifier
            not in _nested_object_field_descriptors
        )

    def std_object_default_descriptor_by_id(
        self, object_id: StandardObjectIdentifier
    ) -> StandardObjectDescriptor | None:
        return self._default_object_descriptors_by_id.get(object_id)

    def std_object_default_descriptor_by_model(
        self, object_model: type[DomainModel]
    ) -> StandardObjectDescriptor | None:
        if direct_match := self._default_object_descriptor_by_model.get(object_model):
            return direct_match
        for model, descriptor in self._default_object_descriptor_by_model.items():
            if issubclass(object_model, model):
                return descriptor
        return None

    def all_std_object_default_descriptors(
        self,
    ) -> tuple[StandardObjectDescriptor, ...]:
        return self._all_standard_object_descriptors

    def all_std_object_default_descriptors_ignore_nested_object_field(
        self,
    ) -> tuple[StandardObjectDescriptor, ...]:
        return self._all_standard_object_descriptors_ignore_nested_object_field

    @classmethod
    def _validate_default_object_model_by_id(
        cls,
        *,
        default_object_descriptor_by_model: Mapping[
            type[DomainModel], StandardObjectDescriptor
        ],
    ) -> frozendict[StandardObjectIdentifier, type[DomainModel]]:
        result = frozendict(
            {
                object_descriptor.object_identifier: object_model
                for object_model, object_descriptor in default_object_descriptor_by_model.items()
            }
        )
        if len(result) != len(default_object_descriptor_by_model):
            logger.error(
                "duplicate object identifiers found",
                object_descriptors={
                    object_descriptor.object_identifier: object_model.__qualname__
                    for object_model, object_descriptor in default_object_descriptor_by_model.items()
                },
            )
            raise IllegalStateError("Duplicate object identifiers found")
        return result

    @classmethod
    def _validate_default_object_descriptors(
        cls, *, domain_models: set[type[DomainModel]]
    ) -> frozendict[type[DomainModel], StandardObjectDescriptor]:
        individually_validated: dict[type[DomainModel], StandardObjectDescriptor] = {
            dm: dm.object_descriptor() for dm in domain_models
        }
        return cls._validate_object_dependencies(
            default_object_descriptor_by_model=individually_validated
        )

    @classmethod
    def _validate_object_dependencies(  # noqa: C901, PLR0912
        cls,
        *,
        default_object_descriptor_by_model: dict[
            type[DomainModel], StandardObjectDescriptor
        ],
    ) -> frozendict[type[DomainModel], StandardObjectDescriptor]:
        # todo(xw): refactor to simplify this
        object_descriptor_by_id: dict[
            StandardObjectIdentifier, StandardObjectDescriptor
        ] = {
            object_descriptor.object_identifier: object_descriptor
            for object_descriptor in default_object_descriptor_by_model.values()
        }
        if len(object_descriptor_by_id) != len(default_object_descriptor_by_model):
            logger.error(
                "duplicate object identifiers found",
                object_descriptors={
                    object_descriptor.object_identifier: object_model.__qualname__
                    for object_model, object_descriptor in default_object_descriptor_by_model.items()
                },
            )
            raise IllegalStateError("Duplicate object identifiers found")
        seen_relationship_ids: set[str] = set()
        for object_id, object_descriptor in object_descriptor_by_id.items():
            # Validate standard object relationship
            relationship: Relationship
            for relationship in [
                *object_descriptor.inbound_relationships,
                *object_descriptor.outbound_relationships,
            ]:
                if str(relationship.id) in seen_relationship_ids:
                    logger.error(
                        "duplicate relationship id found",
                        object_id=object_id,
                        relationship_id=relationship.id,
                    )
                    raise IllegalStateError(
                        f"Duplicate relationship id [{relationship.id}] found"
                    )
                seen_relationship_ids.add(str(relationship.id))

                if not isinstance(
                    relationship.related_object_identifier, StandardObjectIdentifier
                ):
                    continue
                if (
                    relationship.related_object_identifier
                    not in object_descriptor_by_id
                ):
                    logger.error(
                        "missing related object descriptor",
                        object_id=object_id,
                        related_object_id=relationship.related_object_identifier,
                    )
                    raise IllegalStateError(
                        f"Missing related object descriptor for object [{object_id}]"
                        f", related object [{relationship.related_object_identifier}]"
                    )
                related_object_descriptor = object_descriptor_by_id[
                    relationship.related_object_identifier
                ]
                for related_field_id in relationship.ordered_related_field_identifiers:
                    if not related_object_descriptor.field_descriptor(
                        field_identifier=related_field_id
                    ):
                        logger.error(
                            "missing related field descriptor",
                            object_id=object_id,
                            related_object_id=relationship.related_object_identifier,
                            related_field_id=related_field_id,
                        )
                        raise IllegalStateError(
                            f"Missing field descriptor for related "
                            f"field [{related_field_id}] "
                            f"related object [{related_object_descriptor.object_identifier}]"
                            f"of object [{object_id}]"
                        )
            # validate nested properties
            for field in object_descriptor.fields:
                referenced_object_id: StandardObjectIdentifier
                field_type_property = field.field_type_property
                match field_type_property:
                    case NestedObjectFieldProperty() if isinstance(
                        field_type_property.object_identifier, StandardObjectIdentifier
                    ):
                        referenced_object_id = field_type_property.object_identifier
                    case ListFieldProperty() if isinstance(
                        field_type_property.element_field_type_property,
                        NestedObjectFieldProperty,
                    ) and isinstance(
                        field_type_property.element_field_type_property.object_identifier,
                        StandardObjectIdentifier,
                    ):
                        referenced_object_id = field_type_property.element_field_type_property.object_identifier
                    case DictFieldProperty() if isinstance(
                        field_type_property.value_field_type_property,
                        NestedObjectFieldProperty,
                    ) and isinstance(
                        field_type_property.value_field_type_property.object_identifier,
                        StandardObjectIdentifier,
                    ):
                        referenced_object_id = field_type_property.value_field_type_property.object_identifier
                    case _:
                        continue
                if referenced_object_id not in object_descriptor_by_id:
                    logger.error(
                        "missing nested object descriptor",
                        object_id=object_id,
                        field_descriptor=field,
                        referenced_object_id=referenced_object_id,
                    )
                    raise IllegalStateError(
                        f"Missing nested object descriptor for object [{object_id}],"
                        f"with nested object [{referenced_object_id}]"
                    )
        return frozendict(default_object_descriptor_by_model)


system_default_domain_schema_provider_v2: Final[DomainDefaultSchemaProviderV2] = (
    DomainDefaultSchemaProviderV2(
        domain_models={
            AccountV2,
            ContactV2,
            ContactPipelineRole,
            PipelineV2,
            OrganizationUserV2,
            Address,
            TaskV2,
            UserGoal,
            UserOrganizationProfile,
            MeetingV2,
            MeetingParticipant,
            MeetingBotStatusEvent,
            BotError,
            Comment,
            MeetingScreenShareRanges,
            Activity,
            ActivitySubReference,
            PipelineStageSelectListValueLite,
            SelectListValueLite,
            GlobalThread,
            GlobalMessage,
            GlobalMessageErrorInfoV2,
            GlobalMessageEventSummaryV2,
            AttachmentDetailsV2,
            EmailParticipantV2,
            EmailAccountV2,
            EventSchedule,
            EmailTemplate,
            Citation,
            DomainObjectList,
            DomainObjectListItem,
            ContactEmailLite,
            ContactPhoneNumber,
            ContactAccountAssociationLite,
            ProspectingRunV2,
            ProspectingRunResultV2,
            ProspectingCreditUsagePointV2,
            ProspectingFilterFieldOptionsV2,
            ProspectingFilterFieldOptionsFacetV2,
            ProspectingSavedSearchQueryV2,
            ProspectingPhoneNumberDetail,
            ProspectingListEnrollmentResult,
            ProspectingCreditByType,
            ProspectingCreditUsage,
            ProspectingPerson,
            ProspectingCompany,
            ContactAccountRole,
            ContactAccountEmail,
            SequenceV2,
            SequenceParticipant,
            SequenceSchedule,
            SequenceScheduleTime,
            SequenceStats,
            SequenceEnrollment,
            SequenceEnrollmentContact,
            SequenceEnrollmentRun,
            SequenceStepV2,
            SequenceStepExecution,
            SequenceStepVariantV2,
            SignatureV2,
            OutboundDomainV2,
            DomainConfigurationModel,
            BaseAISuggestedValue,
            AccountAISuggestedAnnualRevenue,
            AccountAISuggestedEmployeeCount,
            AccountAISuggestedDescription,
            AccountAISuggestedCategoryList,
            AccountAISuggestedOfficialWebsite,
            ContactAISuggestedLinkedinUrl,
            ContactAISuggestedDisplayName,
            ContactAISuggestedTitle,
            Competition,
            Competitor,
            DecisionCriteria,
            DecisionCriteriaItem,
            DecisionProcess,
            DecisionProcessItem,
            IdentifiedPain,
            IdentifiedPainItem,
            Metric,
            MetricItem,
            PaperProcess,
            PaperProcessItem,
            Chat,
            ChatMessage,
            ContactForGiantTask,
            EmailThreadForGiantTask,
            CitationForGiantTask,
            GiantTask,
            PeopleEnrichmentSummary,
        }
    )
)
