from typing import Any

import asyncpg

from salestech_be.ree_logging import get_logger


class AsyncPostgresDwhConnection:
    """Async connection manager for PostgreSQL Data Warehouse database"""

    def __init__(
        self,
        host: str,
        port: int,
        user: str,
        password: str,
        database: str,
        ssl: bool = False,
    ):
        """
        Initialize an async connection to PostgreSQL DWH

        Args:
            host: PostgreSQL host
            port: PostgreSQL port (default 5432)
            user: Database user
            password: Database password
            database: Database name
            ssl: Whether to use SSL connection
        """
        self.connection_params = {
            "host": host,
            "port": port,
            "user": user,
            "password": password,
            "database": database,
            "ssl": ssl,
        }
        self.connection = None
        self.logger = get_logger(__name__)

    async def connect(self) -> None:
        """Establish connection to PostgreSQL DWH"""
        try:
            self.connection = await asyncpg.connect(**self.connection_params)
            self.logger.info("Connected to PostgreSQL DWH successfully")
        except Exception as e:
            self.logger.error(f"Failed to connect to PostgreSQL DWH: {e}")
            raise

    async def disconnect(self) -> None:
        """Close connection to PostgreSQL DWH"""
        if self.connection:
            await self.connection.close()
            self.connection = None
            self.logger.info("Disconnected from PostgreSQL DWH")

    async def execute_query(self, query: str) -> list[dict[str, Any]]:
        """
        Execute a SELECT query and return results as a list of dictionaries

        Args:
            query: SQL query to execute

        Returns:
            List of dictionaries representing query results
        """
        if not self.connection:
            await self.connect()

        try:
            self.logger.debug(f"Executing query: {query}")
            rows = await self.connection.fetch(query)

            # Convert asyncpg.Record objects to dictionaries
            result = []
            for row in rows:
                result.append(dict(row))

            self.logger.debug(f"Query returned {len(result)} rows")
            return result

        except Exception as e:
            self.logger.error(f"Query execution failed: {e}")
            raise

    async def execute_non_query(self, query: str) -> str:
        """
        Execute a non-SELECT query (INSERT, UPDATE, DELETE, etc.)

        Args:
            query: SQL query to execute

        Returns:
            Status message from the database
        """
        if not self.connection:
            await self.connect()

        try:
            self.logger.debug(f"Executing non-query: {query}")
            result = await self.connection.execute(query)
            self.logger.debug(f"Non-query result: {result}")
            return result

        except Exception as e:
            self.logger.error(f"Non-query execution failed: {e}")
            raise

    async def get_table_schema(self, table_name: str) -> list[dict[str, Any]]:
        """
        Get schema information for a table

        Args:
            table_name: Name of the table

        Returns:
            List of dictionaries containing column information
        """
        query = """
        SELECT
            column_name,
            data_type,
            is_nullable,
            column_default
        FROM information_schema.columns
        WHERE table_name = $1
        ORDER BY ordinal_position
        """

        if not self.connection:
            await self.connect()

        try:
            rows = await self.connection.fetch(query, table_name)
            return [dict(row) for row in rows]
        except Exception as e:
            self.logger.error(f"Failed to get table schema for {table_name}: {e}")
            raise

    async def test_connection(self) -> bool:
        """
        Test the database connection

        Returns:
            True if connection is successful, False otherwise
        """
        try:
            if not self.connection:
                await self.connect()

            # Simple test query
            await self.connection.fetchval("SELECT 1")
            return True

        except Exception as e:
            self.logger.error(f"Connection test failed: {e}")
            return False

    async def __aenter__(self):
        """Async context manager entry"""
        await self.connect()
        return self

    async def __aexit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        """Async context manager exit"""
        await self.disconnect()
