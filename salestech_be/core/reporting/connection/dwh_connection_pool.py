"""
Data Warehouse Connection Pool Manager

Provides efficient connection pooling for PostgreSQL data warehouse queries
with proper resource management, timeouts, and error handling.
"""

import asyncio
from contextlib import asynccontextmanager
from typing import Any, AsyncGenerator, Dict, List

import asyncpg
from asyncpg import Pool

from salestech_be.ree_logging import get_logger


class DwhConnectionPool:
    """Async connection pool manager for PostgreSQL Data Warehouse"""

    def __init__(
        self,
        host: str,
        port: int,
        user: str,
        password: str,
        database: str,
        ssl: bool = False,
        min_size: int = 5,
        max_size: int = 20,
        command_timeout: float = 60.0,
        server_settings: Dict[str, str] | None = None,
    ):
        """
        Initialize connection pool for PostgreSQL DWH

        Args:
            host: PostgreSQL host
            port: PostgreSQL port
            user: Database user
            password: Database password
            database: Database name
            ssl: Whether to use SSL connection
            min_size: Minimum number of connections in pool
            max_size: Maximum number of connections in pool
            command_timeout: Query timeout in seconds
            server_settings: Additional server settings
        """
        self.connection_params = {
            "host": host,
            "port": port,
            "user": user,
            "password": password,
            "database": database,
            "ssl": ssl,
            "min_size": min_size,
            "max_size": max_size,
            "command_timeout": command_timeout,
            "server_settings": server_settings or {},
        }
        self.pool: Pool | None = None
        self.logger = get_logger(__name__)

    async def initialize(self) -> None:
        """Initialize the connection pool"""
        try:
            self.pool = await asyncpg.create_pool(**self.connection_params)
            self.logger.info(
                f"DWH connection pool initialized with {self.connection_params['min_size']}-{self.connection_params['max_size']} connections"
            )
        except Exception as e:
            self.logger.error(f"Failed to initialize DWH connection pool: {e}")
            raise

    async def close(self) -> None:
        """Close the connection pool"""
        if self.pool:
            await self.pool.close()
            self.pool = None
            self.logger.info("DWH connection pool closed")

    @asynccontextmanager
    async def acquire_connection(self) -> AsyncGenerator[asyncpg.Connection, None]:
        """
        Acquire a connection from the pool using context manager
        
        Usage:
            async with pool.acquire_connection() as conn:
                result = await conn.fetch("SELECT * FROM table")
        """
        if not self.pool:
            raise RuntimeError("Connection pool not initialized")

        async with self.pool.acquire() as connection:
            try:
                yield connection
            except Exception as e:
                self.logger.error(f"Error during connection usage: {e}")
                raise

    async def execute_query(
        self, 
        query: str, 
        *args: Any,
        timeout: float | None = None
    ) -> List[Dict[str, Any]]:
        """
        Execute a SELECT query and return results as list of dictionaries

        Args:
            query: SQL query to execute
            *args: Query parameters
            timeout: Query timeout override

        Returns:
            List of dictionaries representing query results
        """
        if not self.pool:
            raise RuntimeError("Connection pool not initialized")

        query_timeout = timeout or self.connection_params["command_timeout"]
        
        try:
            self.logger.debug(f"Executing query with timeout {query_timeout}s: {query}")
            
            async with self.acquire_connection() as conn:
                rows = await asyncio.wait_for(
                    conn.fetch(query, *args),
                    timeout=query_timeout
                )

            # Convert asyncpg.Record objects to dictionaries
            result = [dict(row) for row in rows]
            
            self.logger.debug(f"Query returned {len(result)} rows")
            return result

        except asyncio.TimeoutError:
            self.logger.error(f"Query timeout after {query_timeout}s: {query}")
            raise
        except Exception as e:
            self.logger.error(f"Query execution failed: {e}")
            raise

    async def execute_query_single(
        self, 
        query: str, 
        *args: Any,
        timeout: float | None = None
    ) -> Dict[str, Any] | None:
        """
        Execute a query and return single result or None

        Args:
            query: SQL query to execute
            *args: Query parameters
            timeout: Query timeout override

        Returns:
            Single result dictionary or None
        """
        results = await self.execute_query(query, *args, timeout=timeout)
        return results[0] if results else None

    async def get_table_schema(self, table_name: str) -> List[Dict[str, Any]]:
        """
        Get schema information for a table

        Args:
            table_name: Name of the table

        Returns:
            List of dictionaries containing column information
        """
        query = """
        SELECT
            column_name,
            data_type,
            is_nullable,
            column_default
        FROM information_schema.columns
        WHERE table_name = $1
        ORDER BY ordinal_position
        """
        
        return await self.execute_query(query, table_name)

    async def test_connection(self) -> bool:
        """
        Test the connection pool

        Returns:
            True if connection test is successful, False otherwise
        """
        try:
            if not self.pool:
                await self.initialize()

            result = await self.execute_query_single("SELECT 1 as test")
            return result is not None and result.get("test") == 1

        except Exception as e:
            self.logger.error(f"Connection test failed: {e}")
            return False

    async def get_pool_status(self) -> Dict[str, Any]:
        """
        Get current pool status for monitoring

        Returns:
            Dictionary with pool statistics
        """
        if not self.pool:
            return {"status": "not_initialized"}

        return {
            "status": "active",
            "size": self.pool.get_size(),
            "min_size": self.pool.get_min_size(),
            "max_size": self.pool.get_max_size(),
            "idle_size": self.pool.get_idle_size(),
        }

    async def __aenter__(self) -> "DwhConnectionPool":
        """Async context manager entry"""
        await self.initialize()
        return self

    async def __aexit__(self, *_: Any) -> None:
        """Async context manager exit"""
        await self.close()
