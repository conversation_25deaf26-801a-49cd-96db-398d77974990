from datetime import UTC, datetime
from decimal import Decimal

from salestech_be.core.account.types_v2 import (
    AccountAISuggestedAnnualRevenue,
    AccountAISuggestedCategoryList,
    AccountAISuggestedDescription,
    AccountAISuggestedEmployeeCount,
    AccountAISuggestedOfficialWebsite,
)
from salestech_be.core.account.types_v2 import (
    AccountV2 as DomainAccountV2,
)
from salestech_be.core.common.domain_fetching_hint import DomainToTableModelFieldMapping
from salestech_be.core.common.types import BaseAISuggestedValue
from salestech_be.db.dto.custom_object_data_dto import ExtensionCustomObjectDataGroupDto
from salestech_be.db.models.account import (
    Account as DbAccount,
)
from salestech_be.db.models.address import Address as DbAddress
from salestech_be.db.models.core.types import CreatedSource
from salestech_be.util.validation import not_none


def _ai_value_is_more_recent(
    db_account_updated_at: datetime | None,
    ai_suggested_value_updated_at: datetime | None,
) -> bool:
    if not ai_suggested_value_updated_at:
        return False
    db_account_updated_at = db_account_updated_at or datetime.min.replace(tzinfo=UTC)
    return ai_suggested_value_updated_at > db_account_updated_at


def _is_using_ai_suggested_value(
    db_account_value: str | int | float | list[str] | Decimal | None,
    db_account_created_source: CreatedSource | None,
    db_account_updated_at: datetime | None,
    ai_suggested_value: BaseAISuggestedValue | None = None,
) -> bool:
    # TODO: (taiyan) short term solution to merge ai suggested values
    if not ai_suggested_value or not ai_suggested_value.value:
        return False
    if db_account_value is None:
        return True
    if (
        db_account_created_source
        and db_account_created_source == CreatedSource.CSV_IMPORT
    ):
        return db_account_value is None
    return _ai_value_is_more_recent(
        db_account_updated_at, ai_suggested_value.updated_at
    )


def account_v2_from_db(
    db_account: DbAccount,
    db_address: DbAddress | None = None,
    extension_custom_object_data_group_dto: ExtensionCustomObjectDataGroupDto
    | None = None,
    ai_suggested_annual_revenue: AccountAISuggestedAnnualRevenue | None = None,
    ai_suggested_employee_count: AccountAISuggestedEmployeeCount | None = None,
    ai_suggested_description: AccountAISuggestedDescription | None = None,
    ai_suggested_category_list: AccountAISuggestedCategoryList | None = None,
    ai_suggested_official_website: AccountAISuggestedOfficialWebsite | None = None,
) -> DomainAccountV2:
    street_one = db_address.street_one if db_address else None
    street_two = db_address.street_two if db_address else None
    zip_code = db_address.zip_code if db_address else None
    city = db_address.city if db_address else None
    state = db_address.state if db_address else None
    country = db_address.country if db_address else None

    return DomainAccountV2(
        id=db_account.id,
        display_name=db_account.display_name,
        description=not_none(ai_suggested_description).value
        if _is_using_ai_suggested_value(
            db_account.description,
            db_account.created_source,
            db_account.updated_at,
            ai_suggested_description,
        )
        else db_account.description,
        status=db_account.status,
        access_status=db_account.access_status,
        domain_name=db_account.domain_name,
        official_website=not_none(ai_suggested_official_website).value
        if _is_using_ai_suggested_value(
            db_account.official_website,
            db_account.created_source,
            db_account.updated_at,
            ai_suggested_official_website,
        )
        else db_account.official_website,
        linkedin_url=db_account.linkedin_url,
        x_url=db_account.x_url,
        facebook_url=db_account.facebook_url,
        zoominfo_url=db_account.zoominfo_url,
        street_one=street_one,
        street_two=street_two,
        zip_code=zip_code,
        city=city,
        state=state,
        country=country,
        keyword_list=db_account.keyword_list,
        category_list=not_none(ai_suggested_category_list).value
        if _is_using_ai_suggested_value(
            db_account.category_list,
            db_account.created_source,
            db_account.updated_at,
            ai_suggested_category_list,
        )
        else db_account.category_list,
        technology_list=db_account.technology_list,
        estimated_annual_revenue=not_none(ai_suggested_annual_revenue).value
        if _is_using_ai_suggested_value(
            db_account.estimated_annual_revenue,
            db_account.created_source,
            db_account.updated_at,
            ai_suggested_annual_revenue,
        )
        else db_account.estimated_annual_revenue,
        estimated_employee_count=not_none(ai_suggested_employee_count).value
        if _is_using_ai_suggested_value(
            db_account.estimated_employee_count,
            db_account.created_source,
            db_account.updated_at,
            ai_suggested_employee_count,
        )
        else db_account.estimated_employee_count,
        ai_suggested_annual_revenue=ai_suggested_annual_revenue,
        ai_suggested_employee_count=ai_suggested_employee_count,
        ai_suggested_description=ai_suggested_description,
        ai_suggested_category_list=ai_suggested_category_list,
        ai_suggested_official_website=ai_suggested_official_website,
        organization_id=db_account.organization_id,
        owner_user_id=db_account.owner_user_id,
        research_tldr=db_account.research_tldr,
        research_content=db_account.research_content,
        research_reference_urls=db_account.research_reference_urls or [],
        created_at=db_account.created_at,
        updated_at=db_account.updated_at or db_account.created_at,
        created_by_user_id=db_account.created_by_user_id,
        created_source=db_account.created_source,
        updated_by_user_id=db_account.updated_by_user_id,
        archived_at=db_account.archived_at,
        archived_by_user_id=db_account.archived_by_user_id,
        integrity_job_started_at=db_account.integrity_job_started_at,
        integrity_job_started_by_user_id=db_account.integrity_job_started_by_user_id,
        integrity_job_started_by_job_ids=db_account.integrity_job_started_by_job_ids,
        integrity_job_finished_at=db_account.integrity_job_finished_at,
        integrity_job_finished_by_user_id=db_account.integrity_job_finished_by_user_id,
        custom_field_data=extension_custom_object_data_group_dto.to_generic_custom_field_value(
            extension_id=db_account.id
        )
        if extension_custom_object_data_group_dto
        else None,
        custom_field_data_v2=extension_custom_object_data_group_dto.to_typed_custom_field_data(
            extension_id=db_account.id
        )
        if extension_custom_object_data_group_dto
        else None,
        participant_user_id_list=[
            participant.user_id for participant in db_account.participants or []
        ],
        company_id=db_account.company_id,
    )


account_db_mapping = DomainToTableModelFieldMapping[DomainAccountV2, DbAccount](
    domain_model_tp=DomainAccountV2, table_model_tp=DbAccount
)
