"""
Test the query execution service.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any, List

from salestech_be.core.reporting.connection.materialize_connection import AsyncMaterializeConnection
from salestech_be.core.reporting.service.query_execution_service import QueryExecutionService
from salestech_be.core.reporting.service.query_builder_service import QueryBuilderService
from salestech_be.core.reporting.type.dataset_query_type import (
    QueryConfig, DatasetConfig, FieldColumnConfig, DatasetFieldConfig, FieldConfig
)
from salestech_be.db.dbengine.core import DatabaseEngine


@pytest.fixture
def mock_materialize_connection():
    """Create a mock AsyncMaterializeConnection for testing."""
    connection = AsyncMock(spec=AsyncMaterializeConnection)
    connection.execute_query = AsyncMock(return_value=[
        {"id": 1, "name": "Test Contact"},
        {"id": 2, "name": "Another Contact"}
    ])
    return connection


@pytest.fixture
def mock_db_engine():
    """Create a mock DatabaseEngine for testing."""
    return MagicMock(spec=DatabaseEngine)


@pytest.fixture
def query_execution_service(mock_materialize_connection, mock_db_engine):
    """Create a QueryExecutionService instance for testing."""
    return QueryExecutionService(
        db_connection=mock_materialize_connection,
        db_engine=mock_db_engine
    )


@pytest.mark.asyncio
async def test_execute_query(query_execution_service):
    """Test executing a query directly from a QueryConfig."""
    # Create a simple query config
    dataset_config = DatasetConfig(
        dataset_id="123",
        dataset_name="contacts",
        alias="c"
    )

    field_configs = [
        FieldColumnConfig(
            type="field",
            dataset_field=DatasetFieldConfig(
                dataset=dataset_config,
                field=FieldConfig(
                    field_id="name",
                    field_name="name"
                )
            )
        )
    ]

    config = QueryConfig(
        primary_dataset=dataset_config,
        columns=field_configs
    )

    # Mock the build_query method to return a known SQL string
    with patch.object(
        QueryBuilderService,
        'build_query',
        return_value="SELECT c.name FROM contacts AS c"
    ):
        # Execute the query
        results = await query_execution_service.execute_query(config)

        # Verify the results
        assert len(results) == 2
        assert results[0]["id"] == 1
        assert results[0]["name"] == "Test Contact"

        # Verify that execute_query was called with the correct SQL
        query_execution_service.db_connection.execute_query.assert_called_once_with(
            "SELECT c.name FROM contacts AS c"
        )


@pytest.mark.asyncio
async def test_execute_dataset(query_execution_service):
    """Test executing a query for a dataset."""
    # Mock the build_dataset_query method to return a known SQL string
    with patch.object(
        query_execution_service.query_builder_service,
        'build_dataset_query',
        return_value="SELECT * FROM dataset_table"
    ):
        # Execute the dataset query
        results = await query_execution_service.execute_dataset("dataset-uuid", {"status": "active"})

        # Verify the results
        assert len(results) == 2

        # Verify that execute_query was called with the correct SQL
        query_execution_service.db_connection.execute_query.assert_called_once_with(
            "SELECT * FROM dataset_table"
        )


@pytest.mark.asyncio
async def test_execute_report(query_execution_service):
    """Test executing a query for a report."""
    # Mock the build_report_query method to return a known SQL string
    with patch.object(
        query_execution_service.query_builder_service,
        'build_report_query',
        return_value="SELECT * FROM report_view"
    ):
        # Execute the report query
        results = await query_execution_service.execute_report("report-uuid", {"date": "2023-01-01"})

        # Verify the results
        assert len(results) == 2

        # Verify that execute_query was called with the correct SQL
        query_execution_service.db_connection.execute_query.assert_called_once_with(
            "SELECT * FROM report_view"
        )


@pytest.mark.asyncio
async def test_preview_query(query_execution_service):
    """Test previewing a query from a query config."""
    # Create a simple query config
    dataset_config = DatasetConfig(
        dataset_id="123",
        dataset_name="contacts",
        alias="c"
    )

    config = QueryConfig(
        primary_dataset=dataset_config,
        columns=[]
    )

    # Mock the execute_query method to avoid duplicating test logic
    with patch.object(
        query_execution_service,
        'execute_query',
        return_value=[{"id": 1, "name": "Preview Result"}]
    ):
        # Execute the preview query
        results = await query_execution_service.preview_query(config)

        # Verify the results
        assert len(results) == 1
        assert results[0]["id"] == 1
        assert results[0]["name"] == "Preview Result"

        # Verify that execute_query was called with the correct config
        query_execution_service.execute_query.assert_called_once_with(config)
