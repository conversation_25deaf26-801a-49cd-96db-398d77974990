import pytest
from unittest.mock import Mock

from salestech_be.core.reporting.service.query_builder_service import QueryBuilderService
from salestech_be.core.reporting.type.filter import Filter
from salestech_be.core.reporting.type.query_config import (
    FilterConfig,
    FilterGroup,
    FilterLogic,
    FilterOperator,
    DatasetConfig,
    DatasetFieldConfig,
    FieldConfig,
)
from salestech_be.db.dbengine.core import DatabaseEngine


class TestQueryBuilderService:
    """Test cases for QueryBuilderService"""

    @pytest.fixture
    def mock_db_engine(self):
        """Mock database engine"""
        return Mock(spec=DatabaseEngine)

    @pytest.fixture
    def query_builder_service(self, mock_db_engine):
        """Create QueryBuilderService instance with mocked dependencies"""
        return QueryBuilderService(db_engine=mock_db_engine)

    def test_merge_filters_with_no_filters(self, query_builder_service):
        """Test merge_filters when no new filters are provided"""
        existing_filter_group = FilterGroup(
            filters=[],
            logic=FilterLogic.AND,
        )

        result = query_builder_service.merge_filters(
            filter_group=existing_filter_group,
            filters=None,
        )

        assert result == existing_filter_group

    def test_merge_filters_with_no_existing_group(self, query_builder_service):
        """Test merge_filters when no existing filter group exists"""
        filters = [
            Filter(col="name", op="=", val="test"),
            Filter(col="age", op=">", val=18),
        ]

        result = query_builder_service.merge_filters(
            filter_group=None,
            filters=filters,
            dataset_name="users",
        )

        assert result is not None
        assert result.logic == FilterLogic.AND
        assert len(result.filters) == 2
        assert all(isinstance(f, FilterConfig) for f in result.filters)

    def test_merge_filters_with_and_logic(self, query_builder_service):
        """Test merge_filters with existing AND filter group"""
        # Create existing filter group with AND logic
        existing_filter = FilterConfig(
            dataset_field=DatasetFieldConfig(
                dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                field=FieldConfig(field_id="status", field_name="status"),
            ),
            operator=FilterOperator.EQUALS,
            value="active",
        )
        existing_filter_group = FilterGroup(
            filters=[existing_filter],
            logic=FilterLogic.AND,
        )

        # New filters to merge
        new_filters = [
            Filter(col="name", op="=", val="test"),
        ]

        result = query_builder_service.merge_filters(
            filter_group=existing_filter_group,
            filters=new_filters,
            dataset_name="users",
        )

        assert result is not None
        assert result.logic == FilterLogic.AND
        assert len(result.filters) == 2

    def test_merge_filters_with_or_logic(self, query_builder_service):
        """Test merge_filters with existing OR filter group"""
        # Create existing filter group with OR logic
        existing_filter = FilterConfig(
            dataset_field=DatasetFieldConfig(
                dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                field=FieldConfig(field_id="status", field_name="status"),
            ),
            operator=FilterOperator.EQUALS,
            value="active",
        )
        existing_filter_group = FilterGroup(
            filters=[existing_filter],
            logic=FilterLogic.OR,
        )

        # New filters to merge
        new_filters = [
            Filter(col="name", op="=", val="test"),
        ]

        result = query_builder_service.merge_filters(
            filter_group=existing_filter_group,
            filters=new_filters,
            dataset_name="users",
        )

        assert result is not None
        assert result.logic == FilterLogic.AND  # Should wrap with AND
        assert len(result.filters) == 2
        # First filter should be the original OR group
        assert isinstance(result.filters[0], FilterGroup)
        assert result.filters[0].logic == FilterLogic.OR
        # Second filter should be the new filter config
        assert isinstance(result.filters[1], FilterConfig)

    def test_merge_filters_with_invalid_filter(self, query_builder_service):
        """Test merge_filters with invalid filter that should be skipped"""
        filters = [
            Filter(col="name", op="INVALID_OP", val="test"),  # Invalid operator
            Filter(col="age", op="=", val=18),  # Valid filter
        ]

        result = query_builder_service.merge_filters(
            filter_group=None,
            filters=filters,
            dataset_name="users",
        )

        # Should only have one filter (the valid one)
        assert result is not None
        assert len(result.filters) == 1

    def test_merge_filters_with_all_invalid_filters(self, query_builder_service):
        """Test merge_filters when all filters are invalid"""
        filters = [
            Filter(col="name", op="INVALID_OP", val="test"),
        ]

        result = query_builder_service.merge_filters(
            filter_group=None,
            filters=filters,
            dataset_name="users",
        )

        # Should return None since no valid filters
        assert result is None


class TestFilterConfig:
    """Test cases for FilterConfig.from_filter method"""

    def test_from_filter_basic_equality(self):
        """Test converting basic equality filter"""
        filter_obj = Filter(col="name", op="=", val="test")

        result = FilterConfig.from_filter(filter_obj, "users")

        assert result.operator == FilterOperator.EQUALS
        assert result.value == "test"
        assert result.dataset_field.field.field_name == "name"
        assert result.dataset_field.dataset.dataset_name == "users"

    def test_from_filter_null_value_equals(self):
        """Test converting filter with null value and equals operator"""
        filter_obj = Filter(col="name", op="=", val=None)

        result = FilterConfig.from_filter(filter_obj, "users")

        assert result.operator == FilterOperator.IS_NULL
        assert result.value is None

    def test_from_filter_null_value_not_equals(self):
        """Test converting filter with null value and not equals operator"""
        filter_obj = Filter(col="name", op="!=", val=None)

        result = FilterConfig.from_filter(filter_obj, "users")

        assert result.operator == FilterOperator.IS_NOT_NULL
        assert result.value is None

    def test_from_filter_in_operator(self):
        """Test converting filter with IN operator"""
        filter_obj = Filter(col="status", op="IN", val=["active", "pending"])

        result = FilterConfig.from_filter(filter_obj, "users")

        assert result.operator == FilterOperator.IN
        assert result.value == ["active", "pending"]

    def test_from_filter_case_insensitive_operator(self):
        """Test that operator matching is case insensitive"""
        filter_obj = Filter(col="name", op="like", val="%test%")

        result = FilterConfig.from_filter(filter_obj, "users")

        assert result.operator == FilterOperator.LIKE
        assert result.value == "%test%"

    def test_from_filter_invalid_operator(self):
        """Test that invalid operator raises ValueError"""
        filter_obj = Filter(col="name", op="INVALID", val="test")

        with pytest.raises(ValueError, match="Unsupported filter operator"):
            FilterConfig.from_filter(filter_obj, "users")

    def test_from_filter_invalid_type(self):
        """Test that non-Filter object raises ValueError"""
        with pytest.raises(ValueError, match="Expected Filter object"):
            FilterConfig.from_filter("not a filter", "users")


class TestFilterGroup:
    """Test cases for FilterGroup helper methods"""

    def test_add_filter(self):
        """Test adding a single filter to a group"""
        group = FilterGroup(filters=[], logic=FilterLogic.AND)
        filter_config = FilterConfig(
            dataset_field=DatasetFieldConfig(
                dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                field=FieldConfig(field_id="name", field_name="name"),
            ),
            operator=FilterOperator.EQUALS,
            value="test",
        )

        group.add_filter(filter_config)

        assert len(group.filters) == 1
        assert group.filters[0] == filter_config

    def test_add_filters(self):
        """Test adding multiple filters to a group"""
        group = FilterGroup(filters=[], logic=FilterLogic.AND)
        filter_configs = [
            FilterConfig(
                dataset_field=DatasetFieldConfig(
                    dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                    field=FieldConfig(field_id="name", field_name="name"),
                ),
                operator=FilterOperator.EQUALS,
                value="test",
            ),
            FilterConfig(
                dataset_field=DatasetFieldConfig(
                    dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                    field=FieldConfig(field_id="age", field_name="age"),
                ),
                operator=FilterOperator.GREATER_THAN,
                value=18,
            ),
        ]

        group.add_filters(filter_configs)

        assert len(group.filters) == 2
        assert group.filters == filter_configs

    def test_is_empty(self):
        """Test checking if filter group is empty"""
        empty_group = FilterGroup(filters=[], logic=FilterLogic.AND)
        non_empty_group = FilterGroup(
            filters=[
                FilterConfig(
                    dataset_field=DatasetFieldConfig(
                        dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                        field=FieldConfig(field_id="name", field_name="name"),
                    ),
                    operator=FilterOperator.EQUALS,
                    value="test",
                )
            ],
            logic=FilterLogic.AND,
        )

        assert empty_group.is_empty() is True
        assert non_empty_group.is_empty() is False

    def test_to_sql_empty_group(self):
        """Test SQL generation for empty filter group"""
        group = FilterGroup(filters=[], logic=FilterLogic.AND)

        result = group.to_sql()

        assert result == ""

    def test_to_sql_single_filter(self):
        """Test SQL generation for single filter"""
        filter_config = FilterConfig(
            dataset_field=DatasetFieldConfig(
                dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                field=FieldConfig(field_id="name", field_name="name"),
            ),
            operator=FilterOperator.EQUALS,
            value="test",
        )
        group = FilterGroup(filters=[filter_config], logic=FilterLogic.AND)

        result = group.to_sql()

        # Should not wrap single filter in parentheses
        assert result == "users.name = 'test'"

    def test_to_sql_multiple_filters_and(self):
        """Test SQL generation for multiple filters with AND logic"""
        filter_configs = [
            FilterConfig(
                dataset_field=DatasetFieldConfig(
                    dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                    field=FieldConfig(field_id="name", field_name="name"),
                ),
                operator=FilterOperator.EQUALS,
                value="test",
            ),
            FilterConfig(
                dataset_field=DatasetFieldConfig(
                    dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                    field=FieldConfig(field_id="age", field_name="age"),
                ),
                operator=FilterOperator.GREATER_THAN,
                value=18,
            ),
        ]
        group = FilterGroup(filters=filter_configs, logic=FilterLogic.AND)

        result = group.to_sql()

        # Should wrap multiple filters in parentheses
        assert result == "(users.name = 'test' AND users.age > 18)"

    def test_to_sql_multiple_filters_or(self):
        """Test SQL generation for multiple filters with OR logic"""
        filter_configs = [
            FilterConfig(
                dataset_field=DatasetFieldConfig(
                    dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                    field=FieldConfig(field_id="name", field_name="name"),
                ),
                operator=FilterOperator.EQUALS,
                value="test",
            ),
            FilterConfig(
                dataset_field=DatasetFieldConfig(
                    dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                    field=FieldConfig(field_id="age", field_name="age"),
                ),
                operator=FilterOperator.GREATER_THAN,
                value=18,
            ),
        ]
        group = FilterGroup(filters=filter_configs, logic=FilterLogic.OR)

        result = group.to_sql()

        # Should wrap multiple filters in parentheses with OR
        assert result == "(users.name = 'test' OR users.age > 18)"


class TestIntegration:
    """Integration tests demonstrating complete functionality"""

    @pytest.fixture
    def mock_db_engine(self):
        """Mock database engine"""
        return Mock(spec=DatabaseEngine)

    @pytest.fixture
    def query_builder_service(self, mock_db_engine):
        """Create QueryBuilderService instance with mocked dependencies"""
        return QueryBuilderService(db_engine=mock_db_engine)

    def test_complete_filter_merge_workflow(self, query_builder_service):
        """Test a complete workflow of merging filters with different scenarios"""

        # Start with an existing filter group (OR logic)
        existing_filter = FilterConfig(
            dataset_field=DatasetFieldConfig(
                dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                field=FieldConfig(field_id="status", field_name="status"),
            ),
            operator=FilterOperator.IN,
            value=["active", "pending"],
        )
        existing_filter_group = FilterGroup(
            filters=[existing_filter],
            logic=FilterLogic.OR,
        )

        # Add new filters
        new_filters = [
            Filter(col="name", op="LIKE", val="%john%"),
            Filter(col="age", op=">=", val=18),
            Filter(col="email", op="!=", val=None),  # This should become IS NOT NULL
        ]

        # Merge the filters
        result = query_builder_service.merge_filters(
            filter_group=existing_filter_group,
            filters=new_filters,
            dataset_name="users",
        )

        # Verify the result structure
        assert result is not None
        assert result.logic == FilterLogic.AND  # Should wrap with AND
        assert len(result.filters) == 4  # Original group + 3 new filters

        # First filter should be the original OR group
        assert isinstance(result.filters[0], FilterGroup)
        assert result.filters[0].logic == FilterLogic.OR

        # Remaining filters should be the new FilterConfig objects
        for i in range(1, 4):
            assert isinstance(result.filters[i], FilterConfig)

        # Check that the NULL handling worked correctly
        email_filter = result.filters[3]  # The email filter
        assert email_filter.operator == FilterOperator.IS_NOT_NULL
        assert email_filter.value is None

        # Generate SQL to verify the complete structure
        sql = result.to_sql()
        expected_parts = [
            "users.status IN ('active', 'pending')",   # Original OR group (single filter, no parens)
            "users.name LIKE '%john%'",                 # LIKE filter
            "users.age >= 18",                          # Numeric comparison
            "users.email IS NOT NULL"                   # NULL handling
        ]

        # Verify all expected parts are in the SQL
        for part in expected_parts:
            assert part in sql

        # Verify the overall structure (should be wrapped in parentheses with AND)
        assert sql.startswith("(")
        assert sql.endswith(")")
        assert " AND " in sql

    def test_empty_filter_scenarios(self, query_builder_service):
        """Test various empty filter scenarios"""

        # Test with None filters
        result1 = query_builder_service.merge_filters(None, None)
        assert result1 is None

        # Test with empty list
        result2 = query_builder_service.merge_filters(None, [])
        assert result2 is None

        # Test with existing group and no new filters
        existing_group = FilterGroup(filters=[], logic=FilterLogic.AND)
        result3 = query_builder_service.merge_filters(existing_group, None)
        assert result3 == existing_group

        # Test with existing group and empty new filters
        result4 = query_builder_service.merge_filters(existing_group, [])
        assert result4 == existing_group


class TestMergeFiltersIntoQueryConfig:
    """Test cases for merge_filters_into_query_config method"""

    @pytest.fixture
    def mock_db_engine(self):
        """Mock database engine"""
        return Mock(spec=DatabaseEngine)

    @pytest.fixture
    def query_builder_service(self, mock_db_engine):
        """Create QueryBuilderService instance with mocked dependencies"""
        return QueryBuilderService(db_engine=mock_db_engine)

    @pytest.fixture
    def sample_query_config(self):
        """Create a sample QueryConfig for testing"""
        from salestech_be.core.reporting.type.query_config import (
            QueryConfig,
            DatasetConfig,
            FieldColumnConfig,
            DatasetFieldConfig,
            FieldConfig,
        )

        return QueryConfig(
            columns=[
                FieldColumnConfig(
                    type="field",
                    dataset_field=DatasetFieldConfig(
                        dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                        field=FieldConfig(field_id="name", field_name="name"),
                    ),
                )
            ],
            primary_dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
        )

    def test_merge_filters_into_empty_query_config(self, query_builder_service, sample_query_config):
        """Test merging filters into a QueryConfig with no existing filters"""
        filters = [
            Filter(col="name", op="=", val="test"),
            Filter(col="age", op=">", val=18),
        ]

        # Initially no filter group
        assert sample_query_config.filter_group is None

        # Merge filters
        query_builder_service.merge_filters_into_query_config(
            query_config=sample_query_config,
            filters=filters,
        )

        # Should now have a filter group
        assert sample_query_config.filter_group is not None
        assert sample_query_config.filter_group.logic == FilterLogic.AND
        assert len(sample_query_config.filter_group.filters) == 2

    def test_merge_filters_into_query_config_with_existing_filters(self, query_builder_service, sample_query_config):
        """Test merging filters into a QueryConfig with existing filters"""
        # Add existing filter
        existing_filter = FilterConfig(
            dataset_field=DatasetFieldConfig(
                dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                field=FieldConfig(field_id="status", field_name="status"),
            ),
            operator=FilterOperator.EQUALS,
            value="active",
        )
        sample_query_config.filter_group = FilterGroup(
            filters=[existing_filter],
            logic=FilterLogic.AND,
        )

        # New filters to merge
        new_filters = [
            Filter(col="name", op="=", val="test"),
        ]

        # Merge filters
        query_builder_service.merge_filters_into_query_config(
            query_config=sample_query_config,
            filters=new_filters,
        )

        # Should have merged filters
        assert sample_query_config.filter_group is not None
        assert sample_query_config.filter_group.logic == FilterLogic.AND
        assert len(sample_query_config.filter_group.filters) == 2

    def test_merge_filters_into_query_config_with_or_logic(self, query_builder_service, sample_query_config):
        """Test merging filters into a QueryConfig with existing OR logic"""
        # Add existing filter with OR logic
        existing_filter = FilterConfig(
            dataset_field=DatasetFieldConfig(
                dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                field=FieldConfig(field_id="status", field_name="status"),
            ),
            operator=FilterOperator.EQUALS,
            value="active",
        )
        sample_query_config.filter_group = FilterGroup(
            filters=[existing_filter],
            logic=FilterLogic.OR,
        )

        # New filters to merge
        new_filters = [
            Filter(col="name", op="=", val="test"),
        ]

        # Merge filters
        query_builder_service.merge_filters_into_query_config(
            query_config=sample_query_config,
            filters=new_filters,
        )

        # Should wrap existing OR group with AND logic
        assert sample_query_config.filter_group is not None
        assert sample_query_config.filter_group.logic == FilterLogic.AND
        assert len(sample_query_config.filter_group.filters) == 2
        # First filter should be the original OR group
        assert isinstance(sample_query_config.filter_group.filters[0], FilterGroup)
        assert sample_query_config.filter_group.filters[0].logic == FilterLogic.OR

    def test_merge_filters_into_query_config_with_no_filters(self, query_builder_service, sample_query_config):
        """Test merging None/empty filters into a QueryConfig"""
        original_filter_group = sample_query_config.filter_group

        # Test with None filters
        query_builder_service.merge_filters_into_query_config(
            query_config=sample_query_config,
            filters=None,
        )
        assert sample_query_config.filter_group == original_filter_group

        # Test with empty list
        query_builder_service.merge_filters_into_query_config(
            query_config=sample_query_config,
            filters=[],
        )
        assert sample_query_config.filter_group == original_filter_group

    def test_merge_filters_into_query_config_with_custom_dataset_name(self, query_builder_service, sample_query_config):
        """Test merging filters with custom dataset name"""
        filters = [
            Filter(col="name", op="=", val="test"),
        ]

        # Merge filters with custom dataset name
        query_builder_service.merge_filters_into_query_config(
            query_config=sample_query_config,
            filters=filters,
            dataset_name="custom_dataset",
        )

        # Check that the custom dataset name was used
        assert sample_query_config.filter_group is not None
        filter_config = sample_query_config.filter_group.filters[0]
        assert isinstance(filter_config, FilterConfig)
        assert filter_config.dataset_field.dataset.dataset_name == "custom_dataset"

    def test_merge_filters_into_query_config_uses_primary_dataset_by_default(self, query_builder_service, sample_query_config):
        """Test that primary dataset name is used when no dataset name provided"""
        filters = [
            Filter(col="name", op="=", val="test"),
        ]

        # Merge filters without specifying dataset name
        query_builder_service.merge_filters_into_query_config(
            query_config=sample_query_config,
            filters=filters,
        )

        # Check that the primary dataset name was used
        assert sample_query_config.filter_group is not None
        filter_config = sample_query_config.filter_group.filters[0]
        assert isinstance(filter_config, FilterConfig)
        assert filter_config.dataset_field.dataset.dataset_name == sample_query_config.primary_dataset.dataset_name

    def test_merge_filters_into_query_config_with_invalid_filters(self, query_builder_service, sample_query_config):
        """Test merging filters with some invalid filters"""
        filters = [
            Filter(col="name", op="INVALID_OP", val="test"),  # Invalid
            Filter(col="age", op="=", val=18),  # Valid
        ]

        # Merge filters
        query_builder_service.merge_filters_into_query_config(
            query_config=sample_query_config,
            filters=filters,
        )

        # Should only have the valid filter
        assert sample_query_config.filter_group is not None
        assert len(sample_query_config.filter_group.filters) == 1

    def test_merge_filters_into_query_config_modifies_in_place(self, query_builder_service, sample_query_config):
        """Test that the method modifies the QueryConfig in place"""
        original_config = sample_query_config
        filters = [
            Filter(col="name", op="=", val="test"),
        ]

        # Merge filters
        query_builder_service.merge_filters_into_query_config(
            query_config=sample_query_config,
            filters=filters,
        )

        # Should be the same object, modified in place
        assert sample_query_config is original_config
        assert sample_query_config.filter_group is not None

    def test_merge_filters_into_query_config_integration_example(self, query_builder_service):
        """Integration test showing practical usage of merge_filters_into_query_config"""
        from salestech_be.core.reporting.type.query_config import (
            QueryConfig,
            DatasetConfig,
            FieldColumnConfig,
            DatasetFieldConfig,
            FieldConfig,
            FilterConfig,
            FilterGroup,
            FilterOperator,
            FilterLogic,
        )

        # Create a complex QueryConfig with existing filters
        query_config = QueryConfig(
            columns=[
                FieldColumnConfig(
                    type="field",
                    dataset_field=DatasetFieldConfig(
                        dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                        field=FieldConfig(field_id="name", field_name="name"),
                    ),
                ),
                FieldColumnConfig(
                    type="field",
                    dataset_field=DatasetFieldConfig(
                        dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                        field=FieldConfig(field_id="email", field_name="email"),
                    ),
                ),
            ],
            primary_dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
            filter_group=FilterGroup(
                filters=[
                    FilterConfig(
                        dataset_field=DatasetFieldConfig(
                            dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                            field=FieldConfig(field_id="status", field_name="status"),
                        ),
                        operator=FilterOperator.IN,
                        value=["active", "pending"],
                    ),
                    FilterConfig(
                        dataset_field=DatasetFieldConfig(
                            dataset=DatasetConfig(dataset_id="users", dataset_name="users"),
                            field=FieldConfig(field_id="role", field_name="role"),
                        ),
                        operator=FilterOperator.EQUALS,
                        value="admin",
                    ),
                ],
                logic=FilterLogic.OR,  # Existing OR logic
            ),
        )

        # Add runtime filters
        runtime_filters = [
            Filter(col="created_at", op=">=", val="2024-01-01"),
            Filter(col="last_login", op="!=", val=None),  # Should become IS NOT NULL
            Filter(col="department", op="IN", val=["engineering", "sales"]),
        ]

        # Merge the runtime filters
        query_builder_service.merge_filters_into_query_config(
            query_config=query_config,
            filters=runtime_filters,
        )

        # Verify the structure
        assert query_config.filter_group is not None
        assert query_config.filter_group.logic == FilterLogic.AND  # Should wrap with AND
        assert len(query_config.filter_group.filters) == 4  # Original OR group + 3 new filters

        # First filter should be the original OR group
        original_or_group = query_config.filter_group.filters[0]
        assert isinstance(original_or_group, FilterGroup)
        assert original_or_group.logic == FilterLogic.OR
        assert len(original_or_group.filters) == 2  # Original status and role filters

        # Remaining filters should be the new runtime filters
        for i in range(1, 4):
            assert isinstance(query_config.filter_group.filters[i], FilterConfig)

        # Check specific filter transformations
        last_login_filter = query_config.filter_group.filters[2]  # Should be IS NOT NULL
        assert last_login_filter.operator == FilterOperator.IS_NOT_NULL
        assert last_login_filter.value is None

        department_filter = query_config.filter_group.filters[3]  # Should be IN
        assert department_filter.operator == FilterOperator.IN
        assert department_filter.value == ["engineering", "sales"]

        # Generate SQL to verify the complete structure
        sql = query_config.filter_group.to_sql()

        # Should contain all expected parts
        expected_parts = [
            "users.status IN ('active', 'pending')",
            "users.role = 'admin'",
            "users.created_at >= '2024-01-01'",
            "users.last_login IS NOT NULL",
            "users.department IN ('engineering', 'sales')",
        ]

        for part in expected_parts:
            assert part in sql

        # Should have proper logical structure: (OR group) AND filter1 AND filter2 AND filter3
        assert " OR " in sql  # From the original OR group
        assert " AND " in sql  # From the wrapping AND logic
        assert sql.startswith("(")  # Should be wrapped in parentheses
