import asyncio
import uuid

from salestech_be.core.reporting.type.layout import (
    DashboardLayoutConfig,
    DashboardChartLayoutConfig,
    GridConfig,
    PositionConfig,
)
from salestech_be.db.dao.reporting_repository import ReportingRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.reporting import (
    ReportingChart,
    ReportingDashboard,
    ReportingDashboardChartAssociation,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.time import zoned_utc_now

logger = get_logger(__name__)


async def get_db_engine() -> DatabaseEngine:
    engine = DatabaseEngine(
        str(settings.db_url),
        echo=settings.db_echo,
        pool_size=settings.db_pool_size,
        max_overflow=settings.db_max_overflow,
    )
    if settings.db_conn_prewarm:
        await engine.prewarm_db_connection()
    return engine


async def insert_template_dashboard() -> None:
    """Insert a template dashboard and associate it with all template charts."""
    db_engine = await get_db_engine()
    reporting_repository = ReportingRepository(engine=db_engine)

    now = zoned_utc_now()

    # Create the main template dashboard
    dashboard = ReportingDashboard(
        id=uuid.uuid4(),
        name="Pipeline Analytics Dashboard",
        description="Template dashboard for pipeline analytics and reporting",
        layout_config=DashboardLayoutConfig(
            grid=GridConfig(
                cols=12,  # 12-column grid
                row_height=30,
            )
        ),
        organization_id=None,  # Template dashboard is not org-specific
        created_at=now,
        created_by_user_id=None,
        updated_at=None,
        updated_by_user_id=None,
        deleted_at=None,
        deleted_by_user_id=None,
    )

    # Insert the dashboard
    inserted_dashboard = await reporting_repository.insert(dashboard)
    logger.info(f"Inserted dashboard: {dashboard.name} with ID: {inserted_dashboard.id}")

    # Get all template charts (charts with organization_id = None)
    charts = await reporting_repository._find_by_column_values(
        table_model=ReportingChart,
        organization_id=None,
        exclude_deleted_or_archived=True,
    )

    logger.info(f"Found {len(charts)} template charts to associate with dashboard")

    # Calculate grid positions for charts
    # We'll arrange charts in a grid with 2 charts per row (6 columns each)
    charts_per_row = 2
    chart_width = 6  # 12 columns / 2 charts per row
    chart_height = 8  # Height in grid units

    for i, chart in enumerate(charts):
        try:
            # Calculate position
            row = i // charts_per_row
            col = i % charts_per_row

            x = col * chart_width
            y = row * chart_height

            # Create chart association with layout configuration
            association = ReportingDashboardChartAssociation(
                id=uuid.uuid4(),
                dashboard_id=inserted_dashboard.id,
                chart_id=chart.id,
                layout_config=DashboardChartLayoutConfig(
                    type="chart",
                    position=PositionConfig(
                        x=x,
                        y=y,
                        w=chart_width,
                        h=chart_height,
                        is_resizable=True,
                        min_w=3,  # Minimum width
                        min_h=4,  # Minimum height
                    )
                ),
                organization_id=None,  # Template association is not org-specific
                created_at=now,
                created_by_user_id=None,
                updated_at=None,
                updated_by_user_id=None,
                deleted_at=None,
                deleted_by_user_id=None,
            )

            # Insert the association
            await reporting_repository.insert(association)
            logger.info(f"Associated chart {chart.name} with dashboard at position ({x}, {y})")

        except Exception as e:
            logger.error(f"Error associating chart {chart.name} with dashboard: {e!s}")
            raise

    logger.info(f"Successfully created dashboard with {len(charts)} associated charts")


async def main() -> None:
    """Main function to run the backfill script."""
    logger.info("Starting template dashboard insertion")
    await insert_template_dashboard()
    logger.info("Template dashboard insertion completed")


if __name__ == "__main__":
    asyncio.run(main())