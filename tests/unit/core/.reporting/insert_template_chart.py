import asyncio
import uuid

from salestech_be.core.reporting.type.layout import (
    ChartConfig,
    ChartLayoutConfig,
    VisualizationConfig,
)
from salestech_be.db.dao.reporting_repository import ReportingRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.reporting import (
    ReportingChart,
    ReportingDataset,
    ReportingDatasetSource,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.time import zoned_utc_now

logger = get_logger(__name__)


async def get_db_engine() -> DatabaseEngine:
    engine = DatabaseEngine(
        str(settings.db_url),
        echo=settings.db_echo,
        pool_size=settings.db_pool_size,
        max_overflow=settings.db_max_overflow,
    )
    if settings.db_conn_prewarm:
        await engine.prewarm_db_connection()
    return engine


def create_chart_configs_for_dataset(dataset_name: str) -> list[dict[str, any]]:
    """Create chart configurations based on dataset name and purpose."""
    charts = []

    if "count" in dataset_name.lower():
        # For count-based datasets, create bar charts
        charts.append({
            "name": f"{dataset_name.replace('_', ' ').title()} - Bar Chart",
            "description": f"Bar chart visualization for {dataset_name}",
            "layout_config": ChartLayoutConfig(
                visualization=VisualizationConfig(
                    type="chart",
                    title=f"{dataset_name.replace('_', ' ').title()}",
                    chart=ChartConfig(
                        chart_type="bar",
                        show_legend=True,
                        show_grid=True,
                        show_tooltip=True,
                    )
                )
            )
        })

        # Also create a table view
        charts.append({
            "name": f"{dataset_name.replace('_', ' ').title()} - Table",
            "description": f"Table view for {dataset_name}",
            "layout_config": ChartLayoutConfig(
                visualization=VisualizationConfig(
                    type="table",
                    title=f"{dataset_name.replace('_', ' ').title()} - Data Table",
                )
            )
        })

    elif "trend" in dataset_name.lower() or "aggregated" in dataset_name.lower():
        # For trend datasets, create line charts
        charts.append({
            "name": f"{dataset_name.replace('_', ' ').title()} - Line Chart",
            "description": f"Line chart showing trends for {dataset_name}",
            "layout_config": ChartLayoutConfig(
                visualization=VisualizationConfig(
                    type="chart",
                    title=f"{dataset_name.replace('_', ' ').title()}",
                    chart=ChartConfig(
                        chart_type="line",
                        show_legend=True,
                        show_grid=True,
                        show_tooltip=True,
                    )
                )
            )
        })

    elif "conversion" in dataset_name.lower():
        # For conversion datasets, create funnel or bar charts
        charts.append({
            "name": f"{dataset_name.replace('_', ' ').title()} - Funnel Chart",
            "description": f"Funnel chart for {dataset_name}",
            "layout_config": ChartLayoutConfig(
                visualization=VisualizationConfig(
                    type="chart",
                    title=f"{dataset_name.replace('_', ' ').title()}",
                    chart=ChartConfig(
                        chart_type="bar",
                        show_legend=True,
                        show_grid=True,
                        show_tooltip=True,
                    )
                )
            )
        })

    elif "bucketed" in dataset_name.lower():
        # For bucketed data, create histogram-style charts
        charts.append({
            "name": f"{dataset_name.replace('_', ' ').title()} - Histogram",
            "description": f"Histogram visualization for {dataset_name}",
            "layout_config": ChartLayoutConfig(
                visualization=VisualizationConfig(
                    type="chart",
                    title=f"{dataset_name.replace('_', ' ').title()}",
                    chart=ChartConfig(
                        chart_type="bar",
                        show_legend=False,
                        show_grid=True,
                        show_tooltip=True,
                    )
                )
            )
        })

    else:
        # Default chart types for other datasets
        charts.append({
            "name": f"{dataset_name.replace('_', ' ').title()} - Overview",
            "description": f"Overview chart for {dataset_name}",
            "layout_config": ChartLayoutConfig(
                visualization=VisualizationConfig(
                    type="chart",
                    title=f"{dataset_name.replace('_', ' ').title()}",
                    chart=ChartConfig(
                        chart_type="bar",
                        show_legend=True,
                        show_grid=True,
                        show_tooltip=True,
                    )
                )
            )
        })

        # Add a table view as well
        charts.append({
            "name": f"{dataset_name.replace('_', ' ').title()} - Table View",
            "description": f"Table view for {dataset_name}",
            "layout_config": ChartLayoutConfig(
                visualization=VisualizationConfig(
                    type="table",
                    title=f"{dataset_name.replace('_', ' ').title()} - Data",
                )
            )
        })

    return charts


async def insert_template_charts() -> None:
    """Insert template charts for all template datasets."""
    db_engine = await get_db_engine()
    reporting_repository = ReportingRepository(engine=db_engine)

    # Get all template datasets
    datasets = await reporting_repository._find_by_column_values(
        table_model=ReportingDataset,
        source=ReportingDatasetSource.TEMPLATE,
        exclude_deleted_or_archived=True,
    )

    now = zoned_utc_now()
    total_charts = 0

    for dataset in datasets:
        try:
            # Create chart configurations for this dataset
            chart_configs = create_chart_configs_for_dataset(dataset.name)

            logger.info(f"Creating {len(chart_configs)} charts for dataset {dataset.name}")

            # Insert each chart
            for chart_config in chart_configs:
                chart = ReportingChart(
                    id=uuid.uuid4(),
                    name=chart_config["name"],
                    description=chart_config["description"],
                    dataset_id=dataset.id,
                    layout_config=chart_config["layout_config"],
                    published_by_user_id=None,
                    published_at=None,  # Charts start as unpublished
                    organization_id=None,  # Template charts are not org-specific
                    created_at=now,
                    created_by_user_id=None,
                    updated_at=None,
                    updated_by_user_id=None,
                    deleted_at=None,
                    deleted_by_user_id=None,
                )

                inserted_chart = await reporting_repository.insert(chart)
                logger.info(f"Inserted chart: {chart.name} with ID: {inserted_chart.id}")
                total_charts += 1

        except Exception as e:
            logger.error(f"Error creating charts for dataset {dataset.name}: {e!s}")
            raise

    logger.info(f"Successfully created {total_charts} template charts for {len(datasets)} datasets")


async def main() -> None:
    """Main function to run the backfill script."""
    logger.info("Starting template chart insertion")
    await insert_template_charts()
    logger.info("Template chart insertion completed")


if __name__ == "__main__":
    asyncio.run(main())