import asyncio
import re
import uuid
from typing import Any

from salestech_be.db.dao.reporting_repository import ReportingRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.reporting import (
    ReportingDataset,
    ReportingDatasetField,
    ReportingDatasetFieldDataType,
    ReportingDatasetSource,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.time import zoned_utc_now

logger = get_logger(__name__)


async def get_db_engine() -> DatabaseEngine:
    engine = DatabaseEngine(
        str(settings.db_url),
        echo=settings.db_echo,
        pool_size=settings.db_pool_size,
        max_overflow=settings.db_max_overflow,
    )
    if settings.db_conn_prewarm:
        await engine.prewarm_db_connection()
    return engine


def extract_select_fields_from_sql(sql_content: str) -> list[dict[str, Any]]:
    """Extract field names from SQL SELECT statements."""
    fields = []

    # Remove comments and normalize whitespace
    sql_clean = re.sub(r'--.*?\n', '\n', sql_content)
    sql_clean = re.sub(r'/\*.*?\*/', '', sql_clean, flags=re.DOTALL)
    sql_clean = re.sub(r'\s+', ' ', sql_clean).strip()

    # Find the final SELECT statement (usually the last one)
    select_matches = list(re.finditer(r'\bSELECT\s+(.*?)\s+FROM', sql_clean, re.IGNORECASE | re.DOTALL))

    if not select_matches:
        logger.warning("No SELECT statement found in SQL")
        return fields

    # Use the last SELECT statement
    last_select = select_matches[-1]
    select_clause = last_select.group(1)

    # Split by comma, but be careful about commas inside functions
    field_parts = []
    paren_count = 0
    current_field = ""

    for char in select_clause:
        if char == '(':
            paren_count += 1
        elif char == ')':
            paren_count -= 1
        elif char == ',' and paren_count == 0:
            field_parts.append(current_field.strip())
            current_field = ""
            continue
        current_field += char

    if current_field.strip():
        field_parts.append(current_field.strip())

    # Process each field
    for field_part in field_parts:
        field_part = field_part.strip()
        if not field_part or field_part == '*':
            continue

        # Extract field name and alias
        field_name = None
        display_name = None

        # Check for AS alias
        as_match = re.search(r'\s+AS\s+(\w+)$', field_part, re.IGNORECASE)
        if as_match:
            field_name = as_match.group(1)
            display_name = field_name
        else:
            # Check for implicit alias (space-separated)
            parts = field_part.split()
            if len(parts) >= 2 and not any(keyword in parts[-1].upper() for keyword in ['FROM', 'WHERE', 'GROUP', 'ORDER', 'HAVING']):
                field_name = parts[-1]
                display_name = field_name
            else:
                # Extract from simple column reference
                simple_match = re.search(r'(\w+)\.(\w+)$', field_part)
                if simple_match:
                    field_name = simple_match.group(2)
                    display_name = field_name
                else:
                    # Just use the last word if it looks like a column name
                    word_match = re.search(r'(\w+)$', field_part)
                    if word_match:
                        field_name = word_match.group(1)
                        display_name = field_name

        if field_name:
            # Determine data type based on field name patterns
            data_type = ReportingDatasetFieldDataType.STRING
            field_lower = field_name.lower()

            if any(keyword in field_lower for keyword in ['id', 'uuid']):
                data_type = ReportingDatasetFieldDataType.STRING
            elif any(keyword in field_lower for keyword in ['count', 'rank', 'number']):
                data_type = ReportingDatasetFieldDataType.INTEGER
            elif any(keyword in field_lower for keyword in ['amount', 'value', 'price', 'cost', 'duration', 'days']):
                data_type = ReportingDatasetFieldDataType.DECIMAL
            elif any(keyword in field_lower for keyword in ['created_at', 'updated_at', 'closed_at', 'date', 'time']):
                data_type = ReportingDatasetFieldDataType.DATETIME
            elif any(keyword in field_lower for keyword in ['is_', 'has_']):
                data_type = ReportingDatasetFieldDataType.BOOLEAN

            fields.append({
                'name': field_name,
                'display_name': display_name,
                'data_type': data_type
            })

    return fields


async def insert_template_dataset_fields() -> None:
    """Insert dataset fields for all template datasets."""
    db_engine = await get_db_engine()
    reporting_repository = ReportingRepository(engine=db_engine)

    # Get all template datasets
    datasets = await reporting_repository._find_by_column_values(
        table_model=ReportingDataset,
        source=ReportingDatasetSource.TEMPLATE,
        exclude_deleted_or_archived=True,
    )

    now = zoned_utc_now()

    for dataset in datasets:
        if not dataset.sql_statement:
            logger.warning(f"Dataset {dataset.name} has no SQL statement, skipping")
            continue

        try:
            # Extract fields from SQL
            fields = extract_select_fields_from_sql(dataset.sql_statement)

            logger.info(f"Found {len(fields)} fields for dataset {dataset.name}")

            # Insert each field
            for field_info in fields:
                field = ReportingDatasetField(
                    id=uuid.uuid4(),
                    dataset_id=dataset.id,
                    name=field_info['name'],
                    display_name=field_info['display_name'],
                    data_type=field_info['data_type'],
                    organization_id=None,  # Template fields are not org-specific
                    created_at=now,
                    created_by_user_id=None,
                    updated_at=None,
                    updated_by_user_id=None,
                    deleted_at=None,
                    deleted_by_user_id=None,
                )

                await reporting_repository.create_dataset_field(field)
                logger.info(f"Inserted field: {field.name} for dataset {dataset.name}")

        except Exception as e:
            logger.error(f"Error processing dataset {dataset.name}: {e!s}")
            raise

    logger.info(f"Successfully processed {len(datasets)} datasets")


async def main() -> None:
    """Main function to run the backfill script."""
    logger.info("Starting template dataset field insertion")
    await insert_template_dataset_fields()
    logger.info("Template dataset field insertion completed")


if __name__ == "__main__":
    asyncio.run(main())