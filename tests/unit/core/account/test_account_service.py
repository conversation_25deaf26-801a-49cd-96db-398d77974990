from datetime import UTC, datetime
from decimal import Decimal
from unittest.mock import AsyncMock
from uuid import UUID, uuid4

import pytest

from salestech_be.common.type.metadata.common import ObjectAccessStatus
from salestech_be.core.account.converter import _is_using_ai_suggested_value
from salestech_be.core.account.service.account_query_service import AccountQueryService
from salestech_be.core.account.service.account_service import AccountService
from salestech_be.core.account.types_v2 import (
    AccountAISuggestedAnnualRevenue,
    AccountAISuggestedCategoryList,
    AccountAISuggestedDescription,
    AccountAISuggestedEmployeeCount,
    AccountV2,
    PatchAccountRequest,
)
from salestech_be.core.research_agent.models.company_info import (
    CompanyResearch,
    CompanyResearchInfo,
)
from salestech_be.core.research_agent.types import DBAccountAISuggestedValues
from salestech_be.db.dao.account_repository import AccountRepository
from salestech_be.db.models.account import Account as DbAccount
from salestech_be.db.models.account import AccountStatus
from salestech_be.db.models.core.types import CreatedSource
from salestech_be.db.models.event_schedule import EventSchedule
from salestech_be.util.time import zoned_utc_now
from tests.util.factories import (
    AccountFactory,
    ContactFactory,
    EventScheduleFactory,
)
from tests.util.service_test import ServiceTest


def test_account_state_range() -> None:
    assert AccountStatus.range(le=AccountStatus.SELLING, gt=AccountStatus.TARGET) == [
        AccountStatus.SELLING
    ]
    assert AccountStatus.range(le=AccountStatus.SELLING) == [
        AccountStatus.TARGET,
        AccountStatus.SELLING,
    ]


class TestAccountService(ServiceTest):
    async def test_list_by_ids_returns_empty_list_when_no_accounts_found(
        self,
        account_repository: AsyncMock,
        account_query_service: AccountQueryService,
    ) -> None:
        account_ids = [uuid4()]
        organization_id = uuid4()
        account_repository_list_by_ids(
            account_repository=account_repository,
            input_account_ids=account_ids,
            input_organization_id=organization_id,
            result=[],
        )

        accounts = await account_query_service.list_accounts_v2(
            organization_id=organization_id,
            only_include_account_ids=set(account_ids),
        )
        assert accounts == []

    async def test_patch_public_domain_accounts_no_accounts(
        self,
        account_repository: AsyncMock,
        account_service: AccountService,
    ) -> None:
        organization_id = uuid4()
        user_id = uuid4()
        domain = "gmail.com"

        self.mock_repo_find_by_column_values(
            mock_repository=account_repository,
            input_lookup_fields={
                "domain_name": "gmail-com",
                "official_website": "gmail-com",
                "organization_id": organization_id,
            },
            result=[],
        )

        results = await account_service.patch_public_domain_accounts(
            organization_id=organization_id,
            user_id=user_id,
            domain=domain,
            dry_run=False,
        )

        assert results == []

    async def test_patch_public_domain_accounts_contact_no_primary_email(
        self,
        account_factory: AccountFactory,
        contact_factory: ContactFactory,
        contact_query_service: AsyncMock,
        account_repository: AsyncMock,
        contact_repository: AsyncMock,
        account_service: AccountService,
    ) -> None:
        organization_id = uuid4()
        user_id = uuid4()
        domain = "gmail.com"
        account_id = uuid4()

        account = account_factory.build(
            id=account_id,
            organization_id=organization_id,
            domain_name="gmail-com",
            official_website="gmail-com",
        )
        self.mock_repo_find_by_column_values(
            mock_repository=account_repository,
            input_lookup_fields={
                "domain_name": "gmail-com",
                "official_website": "gmail-com",
                "organization_id": organization_id,
            },
            result=[account],
        )

        contact = contact_factory.build(primary_email=None)
        self.mock_contact_query_service_map_contact_by_primary_account_ids(
            mock_contact_query_service=contact_query_service,
            input_lookup_fields={
                "primary_account_ids": {account_id},
                "organization_id": organization_id,
            },
            result={account_id: [contact]},
        )
        self.mock_contact_query_service_get_primary_emails_by_contact_ids(
            mock_contact_query_service=contact_query_service,
            input_lookup_fields={
                "organization_id": organization_id,
                "contact_ids": {contact.id},
            },
            result={},
        )

        results = await account_service.patch_public_domain_accounts(
            organization_id=organization_id,
            user_id=user_id,
            domain=domain,
            dry_run=False,
        )

        assert results == []

    async def test_patch_public_domain_accounts_contact_correct_for_account(
        self,
        account_factory: AccountFactory,
        contact_factory: ContactFactory,
        account_repository: AsyncMock,
        contact_query_service: AsyncMock,
        contact_repository: AsyncMock,
        account_service: AccountService,
    ) -> None:
        organization_id = uuid4()
        user_id = uuid4()
        domain = "gmail.com"
        account_id = uuid4()

        account = account_factory.build(
            id=account_id,
            organization_id=organization_id,
            domain_name="gmail-com",
            official_website="gmail-com",
            display_name="leon.2",
        )
        self.mock_repo_find_by_column_values(
            mock_repository=account_repository,
            input_lookup_fields={
                "domain_name": "gmail-com",
                "official_website": "gmail-com",
                "organization_id": organization_id,
            },
            result=[account],
        )

        contact = contact_factory.build(primary_email="<EMAIL>")
        self.mock_contact_query_service_map_contact_by_primary_account_ids(
            mock_contact_query_service=contact_query_service,
            input_lookup_fields={
                "primary_account_ids": {account_id},
                "organization_id": organization_id,
            },
            result={account_id: [contact]},
        )
        self.mock_contact_query_service_get_primary_emails_by_contact_ids(
            mock_contact_query_service=contact_query_service,
            input_lookup_fields={
                "organization_id": organization_id,
                "contact_ids": {contact.id},
            },
            result={contact.id: "<EMAIL>"},
        )

        results = await account_service.patch_public_domain_accounts(
            organization_id=organization_id,
            user_id=user_id,
            domain=domain,
            dry_run=False,
        )

        assert results == []

    async def test_patch_public_domain_accounts_contact_no_event_schedule_booking(
        self,
        account_factory: AccountFactory,
        contact_factory: ContactFactory,
        account_repository: AsyncMock,
        contact_query_service: AsyncMock,
        contact_repository: AsyncMock,
        event_schedule_repository: AsyncMock,
        account_service: AccountService,
    ) -> None:
        organization_id = uuid4()
        user_id = uuid4()
        domain = "gmail.com"
        account_id = uuid4()

        account = account_factory.build(
            id=account_id,
            organization_id=organization_id,
            domain_name="gmail-com",
            official_website="gmail-com",
        )
        self.mock_repo_find_by_column_values(
            mock_repository=account_repository,
            input_lookup_fields={
                "domain_name": "gmail-com",
                "official_website": "gmail-com",
                "organization_id": organization_id,
            },
            result=[account],
        )

        contact = contact_factory.build(primary_email="<EMAIL>")
        self.mock_contact_query_service_map_contact_by_primary_account_ids(
            mock_contact_query_service=contact_query_service,
            input_lookup_fields={
                "primary_account_ids": {account_id},
                "organization_id": organization_id,
            },
            result={account_id: [contact]},
        )

        self.mock_contact_query_service_get_primary_emails_by_contact_ids(
            mock_contact_query_service=contact_query_service,
            input_lookup_fields={
                "organization_id": organization_id,
                "contact_ids": {contact.id},
            },
            result={contact.id: "<EMAIL>"},
        )

        mock_event_schedule_repository_get_bookings_by_guest_email(
            mock_event_schedule_repository=event_schedule_repository,
            input_guest_email="<EMAIL>",
            input_organization_id=organization_id,
            result=[],
        )

        results = await account_service.patch_public_domain_accounts(
            organization_id=organization_id,
            user_id=user_id,
            domain=domain,
            dry_run=False,
        )

        assert results == []

    async def test_patch_public_domain_accounts_creates_new_account(
        self,
        account_factory: AccountFactory,
        contact_factory: ContactFactory,
        event_schedule_factory: EventScheduleFactory,
        account_repository: AsyncMock,
        contact_query_service: AsyncMock,
        contact_service: AsyncMock,
        contact_repository: AsyncMock,
        event_schedule_repository: AsyncMock,
        account_service: AccountService,
    ) -> None:
        organization_id = uuid4()
        user_id = uuid4()
        domain = "gmail.com"
        old_account_id = uuid4()
        new_account_id = uuid4()

        account = account_factory.build(
            id=old_account_id,
            organization_id=organization_id,
            domain_name="gmail-com",
            official_website="gmail-com",
        )
        self.mock_repo_find_by_column_values(
            mock_repository=account_repository,
            input_lookup_fields={
                "domain_name": "gmail-com",
                "official_website": "gmail-com",
                "organization_id": organization_id,
            },
            result=[account],
        )

        contact = contact_factory.build(primary_email="<EMAIL>")
        self.mock_contact_query_service_map_contact_by_primary_account_ids(
            mock_contact_query_service=contact_query_service,
            input_lookup_fields={
                "primary_account_ids": {old_account_id},
                "organization_id": organization_id,
            },
            result={old_account_id: [contact]},
        )
        self.mock_contact_query_service_get_primary_emails_by_contact_ids(
            mock_contact_query_service=contact_query_service,
            input_lookup_fields={
                "organization_id": organization_id,
                "contact_ids": {contact.id},
            },
            result={contact.id: "<EMAIL>"},
        )

        mock_event_schedule = event_schedule_factory.build()
        self.mock_repo_find_by_column_values(
            mock_repository=event_schedule_repository,
            input_lookup_fields={
                "guest_email": "<EMAIL>",
                "organization_id": organization_id,
            },
            result=[mock_event_schedule],
        )

        new_account = account.copy(
            update={
                "id": new_account_id,
                "display_name": "leon.2",
                "official_website": "leon-2",
                "domain_name": "leon-2",
            }
        )
        self.mock_repo_insert(
            mock_repository=account_repository,
            result=new_account,
        )

        updated_contact = contact_factory.build(
            id=contact.id, primary_account_id=new_account_id
        )
        self.mock_contact_service_update_primary_account_id(
            mock_contact_service=contact_service,
            input_lookup_fields={
                "user_id": user_id,
                "contact_id": contact.id,
                "organization_id": organization_id,
                "new_account_id": new_account_id,
            },
            result=updated_contact,
        )

        results = await account_service.patch_public_domain_accounts(
            organization_id=organization_id,
            user_id=user_id,
            domain=domain,
            dry_run=False,
        )

        assert results == [(new_account, updated_contact)]

    async def test_get_account_research_resilient_to_missing_fields(
        self,
        account_service: AccountService,
    ) -> None:
        # Arrange
        account_id = uuid4()
        organization_id = uuid4()

        # Mock research agent service to return minimal research data
        account_service.research_agent_service.get_research_for_account_or_none = (  # type: ignore
            AsyncMock(
                return_value=CompanyResearch(
                    intel_company_id=uuid4(),
                    info=CompanyResearchInfo(
                        company_name="Test Company",
                        company_type="Private Company",
                    ),
                    # Required fields with empty lists
                    headcount_activities=[],
                    funding_activities=[],
                    news_activities=[],
                    social_activities=[],
                    # Intentionally omit optional fields to verify resilience:
                    # - funding_profile
                    # - stock_profile
                    # - site_content
                    # - latest_provider_status
                )
            )
        )

        # Act
        result = await account_service.get_account_research(
            account_id=account_id,
            organization_id=organization_id,
        )

        # Assert
        assert result is not None
        assert result.intel_company_id is not None
        assert result.snippet.company_name == "Test Company"
        # Verify empty activity lists are handled properly
        assert result.head_count_history == []
        assert result.funding == []
        assert result.news == []
        assert result.socials == []
        # Verify optional fields are None
        assert result.company_site_content is None

    async def test_account_v2_with_ai_suggested_values(
        self,
        account_factory: AccountFactory,
        account_query_service: AccountQueryService,
    ) -> None:
        # Arrange
        account_id = uuid4()
        organization_id = uuid4()

        # Mock the account repository to return an account
        db_account = account_factory.build(
            id=account_id,
            organization_id=organization_id,
            display_name="Test Company",
        )
        account_query_service.account_repository.list_by_ids = AsyncMock(  # type: ignore
            return_value=[db_account]
        )

        # Mock the research agent service to return AI suggested values
        mock_ai_suggested_values = {
            account_id: DBAccountAISuggestedValues(
                estimated_revenue_higher_bound_usd=Decimal("********"),
                employee_count_range="100-500",
                linkedin_company_description="A test company description",
                linkedin_industries=["Software", "Technology"],
                updated_at=db_account.updated_at,
                company_website="https://www.reevo.ai",
            )
        }

        account_query_service.research_agent_service.map_ai_suggested_values_by_account_ids = AsyncMock()  # type: ignore
        account_query_service.research_agent_service.map_ai_suggested_values_by_account_ids.return_value = mock_ai_suggested_values

        # Act
        accounts = await account_query_service.list_accounts_v2(
            organization_id=organization_id,
            only_include_account_ids={account_id},
        )

        # Assert
        assert len(accounts) == 1
        account = accounts[0]
        assert account.ai_suggested_annual_revenue is not None
        assert account.ai_suggested_annual_revenue.value == ********
        assert account.ai_suggested_employee_count is not None
        assert account.ai_suggested_employee_count.value == 500
        assert account.ai_suggested_description is not None
        assert account.ai_suggested_description.value == "A test company description"
        assert account.ai_suggested_category_list is not None
        assert account.ai_suggested_category_list.value == ["Software", "Technology"]
        assert account.ai_suggested_official_website is not None
        assert account.ai_suggested_official_website.value == "https://www.reevo.ai"

    async def test_account_v2_with_missing_ai_suggested_values(
        self,
        account_factory: AccountFactory,
        account_query_service: AccountQueryService,
    ) -> None:
        # Arrange
        account_id = uuid4()
        organization_id = uuid4()

        # Mock the account repository to return an account
        db_account = account_factory.build(
            id=account_id,
            organization_id=organization_id,
            display_name="Test Company",
        )
        account_query_service.account_repository.list_by_ids = AsyncMock(  # type: ignore
            return_value=[db_account]
        )

        # Mock the research agent service to return empty AI suggested values
        mock_empty_ai_values = {}  # type: ignore

        account_query_service.research_agent_service.map_ai_suggested_values_by_account_ids = AsyncMock()  # type: ignore
        account_query_service.research_agent_service.map_ai_suggested_values_by_account_ids.return_value = mock_empty_ai_values

        # Act
        accounts = await account_query_service.list_accounts_v2(
            organization_id=organization_id,
            only_include_account_ids={account_id},
        )

        # Assert
        assert len(accounts) == 1
        account = accounts[0]
        assert account.ai_suggested_annual_revenue is None
        assert account.ai_suggested_employee_count is None
        assert account.ai_suggested_description is None
        assert account.ai_suggested_category_list is None
        assert account.ai_suggested_official_website is None

    async def test_account_v2_with_error_handling_for_ai_suggested_values(
        self,
        account_factory: AccountFactory,
        account_query_service: AccountQueryService,
    ) -> None:
        # Arrange
        account_id = uuid4()
        organization_id = uuid4()

        # Mock the account repository to return an account
        db_account = account_factory.build(
            id=account_id,
            organization_id=organization_id,
            display_name="Test Company",
        )
        account_query_service.account_repository.list_by_ids = AsyncMock(  # type: ignore
            return_value=[db_account]
        )

        # Mock the research agent service to raise an exception
        account_query_service.research_agent_service.map_ai_suggested_values_by_account_ids = AsyncMock()  # type: ignore
        account_query_service.research_agent_service.map_ai_suggested_values_by_account_ids.side_effect = Exception(
            "Test exception"
        )

        # Act
        accounts = await account_query_service.list_accounts_v2(
            organization_id=organization_id,
            only_include_account_ids={account_id},
        )

        # Assert
        assert len(accounts) == 1
        account = accounts[0]
        assert account.ai_suggested_annual_revenue is None
        assert account.ai_suggested_employee_count is None
        assert account.ai_suggested_description is None
        assert account.ai_suggested_category_list is None
        assert account.ai_suggested_official_website is None

    async def test_ai_suggested_values_merged_based_on_timestamp(
        self,
        account_factory: AccountFactory,
        account_query_service: AccountQueryService,
    ) -> None:
        """Test that AI values are merged based on timestamp comparison."""
        # Arrange
        account_id = uuid4()
        organization_id = uuid4()

        # Test case 1: AI values are newer than account values
        account_updated_at = datetime(2023, 1, 1, tzinfo=UTC)
        ai_updated_at = datetime(2023, 2, 1, tzinfo=UTC)  # Newer than account

        # Create account with original values
        db_account = account_factory.build(
            id=account_id,
            organization_id=organization_id,
            display_name="Test Company",
            description="Original description",
            estimated_annual_revenue=Decimal("1000000"),
            updated_at=account_updated_at,
        )

        # Mock repository to return our account
        account_query_service.account_repository.list_by_ids = AsyncMock(  # type: ignore
            return_value=[db_account]
        )

        # Create mock AI suggested values with newer timestamp
        mock_ai_suggested_values_newer = {
            account_id: DBAccountAISuggestedValues(
                estimated_revenue_higher_bound_usd=Decimal("2000000"),
                employee_count_range="100-500",
                linkedin_company_description="AI suggested description",
                linkedin_industries=["Software", "Technology"],
                updated_at=ai_updated_at,
                company_website="https://www.reevo.ai",
            )
        }

        account_query_service.research_agent_service.map_ai_suggested_values_by_account_ids = AsyncMock()  # type: ignore
        account_query_service.research_agent_service.map_ai_suggested_values_by_account_ids.return_value = mock_ai_suggested_values_newer

        # Act
        accounts = await account_query_service.list_accounts_v2(
            organization_id=organization_id,
            only_include_account_ids={account_id},
        )

        # Assert
        assert len(accounts) == 1
        account = accounts[0]

        # Since AI values are newer, they should be used to update the account values
        assert account.estimated_annual_revenue == 2000000
        assert account.description == "AI suggested description"

        # Test case 2: AI values are older than account values
        # Create a new account with a newer timestamp instead of modifying the existing one
        newer_account_updated_at = datetime(2023, 3, 1, tzinfo=UTC)
        older_ai_updated_at = datetime(2023, 2, 1, tzinfo=UTC)

        # Create a new account instance with newer timestamp
        newer_db_account = account_factory.build(
            id=account_id,
            organization_id=organization_id,
            display_name="Test Company",
            description="Original description",
            estimated_annual_revenue=Decimal("1000000"),
            updated_at=newer_account_updated_at,  # Account is newer
        )

        # Update mock to return the newer account
        account_query_service.account_repository.list_by_ids = AsyncMock(  # type: ignore
            return_value=[newer_db_account]
        )

        # Create mock AI suggested values with older timestamp
        mock_ai_suggested_values_older = {
            account_id: DBAccountAISuggestedValues(
                estimated_revenue_higher_bound_usd=Decimal("2000000"),
                employee_count_range="100-500",
                linkedin_company_description="AI suggested description",
                linkedin_industries=["Software", "Technology"],
                updated_at=older_ai_updated_at,
            )
        }

        account_query_service.research_agent_service.map_ai_suggested_values_by_account_ids = AsyncMock()  # type: ignore
        account_query_service.research_agent_service.map_ai_suggested_values_by_account_ids.return_value = mock_ai_suggested_values_older

        # Act again with updated instances
        accounts = await account_query_service.list_accounts_v2(
            organization_id=organization_id,
            only_include_account_ids={account_id},
        )

        # Assert
        assert len(accounts) == 1
        account = accounts[0]

        # Now the AI values should not be applied, original account values should remain
        assert account.estimated_annual_revenue == Decimal("1000000")
        assert account.description == "Original description"

    async def test_is_using_ai_suggested_value_csv_import(
        self,
        account_factory: AccountFactory,
    ) -> None:
        """Test that AI suggested values are used correctly for accounts imported from CSV."""
        # Arrange
        # Create test account with CSV_IMPORT source
        csv_import_account = account_factory.build(
            created_source=CreatedSource.CSV_IMPORT,
            updated_at=datetime(2023, 1, 1, tzinfo=UTC),
            # Set some fields as None and others with values to test existence checks
            description=None,  # Null field
            category_list=["Existing", "Categories"],  # Not null
            estimated_annual_revenue=None,  # Null field
            estimated_employee_count=500,  # Not null
        )

        # Create test account with different source
        normal_account = account_factory.build(
            created_source=CreatedSource.ACTIVITY_CAPTURE,
            updated_at=datetime(2023, 1, 1, tzinfo=UTC),
            description="Normal description",
        )

        # Create different types of AI suggested values
        ai_desc = AccountAISuggestedDescription(
            value="AI suggested description",
            updated_at=datetime(2023, 2, 1, tzinfo=UTC),
        )

        ai_categories = AccountAISuggestedCategoryList(
            value=["AI", "Suggested", "Categories"],
            updated_at=datetime(2023, 2, 1, tzinfo=UTC),
        )

        ai_revenue = AccountAISuggestedAnnualRevenue(
            value=Decimal("1000000"),
            updated_at=datetime(2023, 2, 1, tzinfo=UTC),
        )

        ai_employee_count = AccountAISuggestedEmployeeCount(
            value=1000,
            updated_at=datetime(2023, 2, 1, tzinfo=UTC),
        )

        # Act & Assert for CSV_IMPORT accounts using _imported_value_not_existing
        # With the new logic, we return TRUE for fields that DON'T exist (are null)

        # For description (which is null in the account), should return TRUE
        assert _is_using_ai_suggested_value(
            csv_import_account.description,
            csv_import_account.created_source,
            csv_import_account.updated_at,
            ai_desc,
        )

        # For categories (which exists in the account), should return FALSE
        assert not _is_using_ai_suggested_value(
            csv_import_account.category_list,
            csv_import_account.created_source,
            csv_import_account.updated_at,
            ai_categories,
        )

        # For revenue (null in account), should return TRUE
        assert _is_using_ai_suggested_value(
            csv_import_account.estimated_annual_revenue,
            csv_import_account.created_source,
            csv_import_account.updated_at,
            ai_revenue,
        )

        # For employee count (exists in account), should return FALSE
        assert not _is_using_ai_suggested_value(
            csv_import_account.estimated_employee_count,
            csv_import_account.created_source,
            csv_import_account.updated_at,
            ai_employee_count,
        )

        # For normal account with newer AI value, should return TRUE based on timestamp
        assert _is_using_ai_suggested_value(
            normal_account.description,
            normal_account.created_source,
            normal_account.updated_at,
            ai_desc,
        )

        # Test other conditions for completeness
        # No AI value should return FALSE
        assert not _is_using_ai_suggested_value(
            normal_account.description,
            normal_account.created_source,
            normal_account.updated_at,
            None,
        )

        # AI value without value should return FALSE
        empty_ai_value = AccountAISuggestedDescription(
            value="", updated_at=datetime(2023, 2, 1, tzinfo=UTC)
        )
        assert not _is_using_ai_suggested_value(
            normal_account.description,
            normal_account.created_source,
            normal_account.updated_at,
            empty_ai_value,
        )

        # Older AI value should return FALSE for normal accounts
        older_ai_value = AccountAISuggestedDescription(
            value="AI suggested value",
            updated_at=datetime(2022, 12, 1, tzinfo=UTC),  # Older than account
        )
        assert not _is_using_ai_suggested_value(
            normal_account.description,
            normal_account.created_source,
            normal_account.updated_at,
            older_ai_value,
        )

    def test_is_using_ai_suggested_value_when_db_value_is_none(self) -> None:
        """Test that _is_using_ai_suggested_value returns True when db_account_value is None."""
        # Arrange
        db_account_value = None
        db_account_created_source = None
        db_account_updated_at = datetime(2023, 1, 1, tzinfo=UTC)

        ai_suggested_value = AccountAISuggestedDescription(
            value="AI suggested description",
            updated_at=datetime(2023, 2, 1, tzinfo=UTC),
        )

        # Act & Assert
        # When db_account_value is None, the function should return True regardless of other parameters
        assert _is_using_ai_suggested_value(
            db_account_value,
            db_account_created_source,
            db_account_updated_at,
            ai_suggested_value,
        )

    async def test_patch_account_linkedin_url_deletes_intel_company_association(
        self,
        account_factory: AccountFactory,
        account_service: AccountService,
    ) -> None:
        """Test that updating linkedin_url calls delete_intel_company_association_by_account_id."""
        # Arrange
        account_id = uuid4()
        organization_id = uuid4()
        user_id = uuid4()
        new_linkedin_url = "https://www.linkedin.com/company/new-company"

        # Create test account
        db_account = account_factory.build(
            id=account_id,
            organization_id=organization_id,
            linkedin_url="https://www.linkedin.com/company/old-company",
            display_name="Test Company",
        )

        # Set up mock return values
        account_v2 = AccountV2(
            id=account_id,
            organization_id=organization_id,
            display_name="Test Company",
            linkedin_url=new_linkedin_url,
            # Add other required fields
            status=AccountStatus.TARGET,
            state=AccountStatus.TARGET.value,
            created_by_user_id=user_id,
            created_at=zoned_utc_now(),
            updated_by_user_id=user_id,
            updated_at=zoned_utc_now(),
            owner_user_id=user_id,
            access_status=ObjectAccessStatus.ACTIVE,
        )

        # Setup mocks with patch
        account_service.account_repository = AsyncMock()
        account_service.account_repository.update_by_tenanted_primary_key.return_value = db_account

        account_service.account_query_service = AsyncMock()
        account_service.account_query_service.get_account_v2.return_value = account_v2

        account_service.research_agent_service = AsyncMock()

        # Create patch request with linkedin_url update
        patch_request = PatchAccountRequest(linkedin_url=new_linkedin_url)

        # Act
        result = await account_service.patch_by_id_v2(
            organization_id=organization_id,
            user_id=user_id,
            account_id=account_id,
            request=patch_request,
        )

        # Assert
        account_service.research_agent_service.delete_intel_company_association_by_account_id.assert_called_once_with(
            account_id=account_id,
            user_id=user_id,
        )
        assert result.linkedin_url == new_linkedin_url

    async def test_patch_account_official_website_deletes_intel_company_association(
        self,
        account_factory: AccountFactory,
        account_service: AccountService,
    ) -> None:
        """Test that updating official_website calls delete_intel_company_association_by_account_id."""
        # Arrange
        account_id = uuid4()
        organization_id = uuid4()
        user_id = uuid4()
        new_website = "https://www.newcompany.com"

        # Create test account
        db_account = account_factory.build(
            id=account_id,
            organization_id=organization_id,
            official_website="https://www.oldcompany.com",
            domain_name="oldcompany-com",
            display_name="Test Company",
        )

        # Set up mock return values
        account_v2 = AccountV2(
            id=account_id,
            organization_id=organization_id,
            display_name="Test Company",
            official_website=new_website,
            domain_name="newcompany-com",
            # Add other required fields
            status=AccountStatus.TARGET,
            state=AccountStatus.TARGET.value,
            created_by_user_id=user_id,
            created_at=zoned_utc_now(),
            updated_by_user_id=user_id,
            updated_at=zoned_utc_now(),
            owner_user_id=user_id,
            access_status=ObjectAccessStatus.ACTIVE,
        )

        # Setup mocks with patch
        account_service.account_repository = AsyncMock()
        account_service.account_repository.update_by_tenanted_primary_key.return_value = db_account

        account_service.account_query_service = AsyncMock()
        account_service.account_query_service.get_account_v2.return_value = account_v2

        account_service.research_agent_service = AsyncMock()

        # Create patch request with official_website update
        patch_request = PatchAccountRequest(official_website=new_website)

        # Act
        result = await account_service.patch_by_id_v2(
            organization_id=organization_id,
            user_id=user_id,
            account_id=account_id,
            request=patch_request,
        )

        # Assert
        account_service.research_agent_service.delete_intel_company_association_by_account_id.assert_called_once_with(
            account_id=account_id,
            user_id=user_id,
        )
        assert result.official_website == new_website


def account_repository_list_by_ids(
    account_repository: AsyncMock,
    input_account_ids: list[UUID],
    input_organization_id: UUID,
    result: list[DbAccount],
) -> None:
    def lookup(ids: list[UUID], organization_id: UUID) -> list[DbAccount]:
        assert ids == input_account_ids
        assert organization_id == input_organization_id
        return result

    account_repository.list_by_ids = AsyncMock(side_effect=lookup)


def mock_event_schedule_repository_get_bookings_by_guest_email(
    mock_event_schedule_repository: AsyncMock,
    input_guest_email: str,
    input_organization_id: UUID,
    result: list[EventSchedule],
) -> None:
    def lookup(*, guest_email: str, organization_id: UUID) -> list[EventSchedule]:
        assert guest_email == input_guest_email
        assert organization_id == input_organization_id
        return result

    mock_event_schedule_repository.get_event_schedule_bookings_by_guest_email = (
        AsyncMock(side_effect=lookup)
    )


@pytest.fixture
def account_repository() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def contact_repository() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def contact_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def contact_query_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def event_schedule_repository() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def account_service(
    account_repository: AccountRepository,
    contact_service: AsyncMock,
    contact_query_service: AsyncMock,
    event_schedule_repository: AsyncMock,
) -> AccountService:
    return AccountService(
        account_repository=account_repository,
        contact_service=contact_service,
        contact_query_service=contact_query_service,
        event_schedule_repository=event_schedule_repository,
        address_repository=AsyncMock(),
        custom_object_service=AsyncMock(),
        account_query_service=AsyncMock(),
        research_agent_service=AsyncMock(),
        approval_service=AsyncMock(),
        stage_criteria_service=AsyncMock(),
        notification_service=AsyncMock(),
        pipeline_repository=AsyncMock(),
        crm_sync_push_service=AsyncMock(),
        feature_flag_service=AsyncMock(),
    )


@pytest.fixture
def account_query_service(account_repository: AccountRepository) -> AccountQueryService:
    return AccountQueryService(
        account_repository=account_repository,
        contact_repository=AsyncMock(),
        address_repository=AsyncMock(),
        custom_object_service=AsyncMock(),
        feature_flag_service=AsyncMock(),
        research_agent_service=AsyncMock(),
    )
